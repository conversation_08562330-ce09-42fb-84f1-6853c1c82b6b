# inference

在线推理

# matrix 配置
ENV_NAME：test preview prod
ACCESS_KEY
ACCESS_SECRET

# mac 本地配置测试
```shell
# 尝试过 3.13，hera 中 mi-otel-pytho 依赖的 atomic 不支持，所以用 3.12
# 配置虚拟环境时 miniforge3 软件兼容性更佳，下载链接 https://github.com/conda-forge/miniforge/releases
# miniforge3安装可参考 https://blog.csdn.net/m0_61998604/article/details/142588443
#环境配置环节
conda create -n inference python=3.12
# mi-otel-python 需要
conda install snappy
pip install -i https://pkgs.d.xiaomi.net/artifactory/api/pypi/pypi-virtual/simple -r requirements.txt
pip install -i https://pkgs.d.xiaomi.net/artifactory/api/pypi/pypi-virtual/simple -r requirements.hera.txt
# 安装前后端用到的 streamlit 等依赖，本地测试算法服务
pip install -i https://pkgs.d.xiaomi.net/artifactory/api/pypi/pypi-virtual/simple -r requirements.st.txt

#运行项目环节
# 注意一定要cd 到 inference 项目里 然后运行以下指令
# 如果启动demo.py文件，则开启两个终端分别执行下两行带代码即可
# 启动算法服务(日志和 conda 换成自己的路径)
export LOG_DIR=/Users/<USER>/workspace/inference/log;/opt/miniconda3/envs/inference/bin/uvicorn api.app:app --host 0.0.0.0 --port 7888 --reload --workers 1
# 启动 demo
/opt/miniconda3/envs/inference/bin/streamlit run /Users/<USER>/workspace/inference/Home.py -- --project_dir /Users/<USER>/workspace/inference
# 如果运行 direct_chat.py 启动算法服务和运行指令放一块执行
export LOG_DIR=/Users/<USER>/workspace/inference/log REDIS_PASSWORD=CelL5iAW7_ZFGWml5WEJAZqhNGeOlxhr OPENAI_API_KEY=9gqlMgEGqn1Z3LKITPhO1mIpmSqCF5sIk2koamKQz9tT9pLzcDu0JQQJ99BFACi0881XJ3w3AAABACOGHAFw;/Users/<USER>/miniforge3/envs/inference/bin/streamlit run /Users/<USER>/workspace/inference/direct_chat.py -- --project_dir /Users/<USER>/workspace/inference

```
如果是环境问题（比如 docker 中的依赖安装导致的），可以在本地打镜像并本地起动：
```shell
docker build -t micr.cloud.mioffice.cn/global_copilot/inference:0.0.1 .
docker run -p 7888:7888 micr.cloud.mioffice.cn/global_copilot/inference:0.0.1
```

# demo 配置
[访问地址](http://************:443/)
```shell
# 拷贝 demo.service
sudo cp config/demo.service /etc/systemd/system/
# 重新加载（修改时需要）
systemctl daemon-reload
# 启动 demo.service
systemctl status demo.service
# 配置开机自启
systemctl enable demo.service
```


# 项目模板
如果要新起一个 python 后端服务项目，可以切到本项目的 init 分支，那里有基础的配置和结构。

# 项目参考文档
https://xiaomi.f.mioffice.cn/wiki/CxtcwgDRpiSXklk2WMikXFXO4th
