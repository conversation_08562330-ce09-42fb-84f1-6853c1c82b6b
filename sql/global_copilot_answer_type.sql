CREATE TABLE `global_copilot_answer_type` (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `answer_type` int(11) NOT NULL DEFAULT '1' COMMENT '消息类型：1-正常回答，输出文本 2-非手机问题拒答 3-机型不一致拒答 4-没相关知识拒答 5-销售信息拒答 6-机型不支持拒答 7-用户确认商品 8-双机对比 9-给出候选项 10-双机对比机型识别模糊 11-未识别到机型 12-非 3c 数码产品的自由类问题的拒答 13-双机对比机型不支持非小米机型之间的对比 14-自由问答中可以回答的部分 15-请求已取消 16-双机对比参数数据库查询失败拒答（后面我们「只增不改」，可能有些会弃用）',
  `display_content` varchar(255) NOT NULL DEFAULT '' COMMENT '看板上展示的文案',
  `item_list_type` int(11) NOT NULL DEFAULT '2' COMMENT '在统计时，该问题是否应该关联 item_list 中的 spu：1-关联 2-不做关联。我们约定：如果关联，则 item_list 第一个是吸顶机型（主机型），第二个是次机型',
  `is_deleted` int(11) NOT NULL DEFAULT '2' COMMENT '是否弃用 1-弃用 2-未弃用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_answer_type` (`answer_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='answerType 维度表：1. 决定看板展示什么文案，2. 在将问题归结到 spu 时，作为是否将 item_list 中的 spu 作为需要统计的依据';

INSERT INTO `global_copilot_answer_type` (`answer_type`, `display_content`, `item_list_type`, `is_deleted`) VALUES
(1, '正常回答', 2, 2),
(2, '非手机问题拒答', 2, 2),
(3, '机型不一致拒答', 2, 2),
(4, '没相关知识拒答', 2, 2),
(5, '销售信息拒答', 2, 2),
(6, '机型不支持拒答', 2, 2),
(7, '用户确认商品', 2, 2),
(8, '双机对比', 1, 2),
(9, '给出候选项', 2, 2),
(10, '双机对比机型识别模糊', 2, 2),
(11, '未识别到机型', 2, 2),
(12, '非3C数码产品拒答', 2, 2),
(13, '双机对比非小米机型拒答', 2, 2),
(14, '自由问答部分回答', 2, 2),
(15, '请求已取消', 2, 2),
(16, '双机对比参数查询失败', 2, 2);
