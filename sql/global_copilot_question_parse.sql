CREATE TABLE `global_copilot_answer_type` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增 ID，主键',
  `conversation_id` varchar(255) NOT NULL DEFAULT '' COMMENT '会话 id',
  `question_id` varchar(255) NOT NULL DEFAULT '' COMMENT '问题 id',
  `add_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `question_content` text COMMENT '问题内容',
  `response` text COMMENT '模型回复内容',
  `first_token_elapse` mediumint(8) unsigned NOT NULL DEFAULT '0' COMMENT '首token耗时(单位/毫秒)',
  `request_receive_time` bigint(15) DEFAULT '0' COMMENT '请求接收时间戳，精确到毫秒',
  `answer_start_time` bigint(15) DEFAULT '0' COMMENT '回答开始时间戳，精确到毫秒',
  `answer_finish_time` bigint(15) DEFAULT '0' COMMENT '回答结束时间戳，精确到毫秒',
  `total_tokens` int(10) DEFAULT '0' COMMENT '消耗的 token 数量',
  `answer_type` int(11) NOT NULL DEFAULT '1' COMMENT '回复类型：1-正常回答 2-非手机问题拒答 3-机型不一致拒答 4-没相关知识拒答 5-销售信息拒答 6-机型不支持拒答',
  `intents` varchar(500) DEFAULT NULL COMMENT '意图列表，多个用英文逗号 '','' 拼接，可能为空（比如识别不出意图）',
  `selected_item_names` varchar(255) DEFAULT NULL COMMENT '用户选择的商品列表，多个用英文逗号 '','' 拼接',
  `actual_item_names` varchar(255) DEFAULT NULL COMMENT '从问题中提取商品列表，多个用英文逗号 '','' 拼接',
  `item_attributes` varchar(500) DEFAULT NULL COMMENT '从问题中提取出涉及的商品属性，电池、屏幕等，多个用英文逗号 '','' 拼接',
  `model_version` varchar(255) NOT NULL DEFAULT '' COMMENT '模型版本',
  `dt` date NOT NULL DEFAULT '2025-04-23' COMMENT '日期（分区字段）格式 20250423',
  `chat_request` text COMMENT '请求内容',
  `prompt` text COMMENT '调用大模型的提示词',
  `system_language` int(11) NOT NULL DEFAULT '1' COMMENT '用户设置里的语言 1-中文 2-英语 3-印尼',
  `input_language` int(11) NOT NULL DEFAULT '1' COMMENT '实际问的语言 1-中文 2-英语 3-印尼',
  `output_language` int(11) NOT NULL DEFAULT '1' COMMENT '实际输出语言 1-中文 2-英语 3-印尼',
  `area` int(11) NOT NULL DEFAULT '1' COMMENT '地区 1-印尼 ; 国家（地区）',
  `version` int(11) DEFAULT NULL COMMENT '算法服务流程版本',
  `response_id` varchar(255) DEFAULT '' COMMENT '请求id-response_id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2226 DEFAULT CHARSET=utf8mb4 COMMENT='问题分析表，国际copilot（印尼区） MVP 版本线上产生的临时数据暂存表，用于后续数据展示和分析'
