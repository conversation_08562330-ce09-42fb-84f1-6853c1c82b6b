CREATE TABLE `doc_trace_source` (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `item_name` varchar(255) NOT NULL DEFAULT '' COMMENT '产品名',
  `doc_type` int(11) NOT NULL DEFAULT '1' COMMENT '文档类型 1-常见问答（faq）2-培训文档（sale tools）',
  `title` text COMMENT '文档标题，doc_type=1（faq）则 title 为问题',
  `content_list` text COMMENT '文档内容，json 列表形式存储（存原始数据，而不是渲染后的 html，因为如果样式改了，可能就失效了），第一个元素 text 表示文本，image 表示图的 url: [("text", "this is a mock text"), ("image", "<image_url>")]',
  `raw_data` text COMMENT '以 json 格式存储可用来回溯的信息，比如 faq 的文章公共编号、产品线、标题等，如果是 faq 则再存一下它的 idx（多个）',
  `is_deleted` int(11) NOT NULL DEFAULT '2' COMMENT '是否弃用 1-弃用 2-未弃用',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='答案溯源表';
