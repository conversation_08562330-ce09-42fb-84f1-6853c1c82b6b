import json
import traceback
import requests

from tqdm import tqdm
from util.llm_util import translate

from concurrent.futures import ThreadPoolExecutor, as_completed

def call_llm_with_json(prompt, api_key_json="app-jg7AqVfXZSVfWUB6nKNWbSHr", chat_request_id="unknown"):
    url = "https://mify-be.pt.xiaomi.com/api/v1/chat-messages"
    headers = {
        "Authorization": f"Bearer {api_key_json}",
        "Content-Type": "application/json",
    }
    data = {
        "inputs": {},
        "query": prompt,
        "response_mode": "blocking",
        "user": f"chat_request_id-{chat_request_id}",
    }

    try:
        response = requests.post(url, headers=headers, data=json.dumps(data))
        if response.status_code == 200:
            result = response.json()
            # output_dict = result["data"]["outputs"]
            return result["answer"]
    except Exception as e:
        traceback.print_exc()

# 定义读取文本文件的函数
def read_txt_file(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        return file.read()
    
def read_json_file(infile):
    with open(infile, 'r') as fin:
        dataset = json.load(fin)
    return dataset

# 定义转换函数
def convert_to_json(data):
    entries = dict()
    
    # 按照双换行符分割数据
    raw_entries = data.strip().split("\n\n")
    
    for entry in raw_entries:
        # 按行分割每个条目
        lines = entry.strip().split("\n")
        
        # 创建一个字典用于存储每个条目的字段
        entry_dict = {}
        for line in lines:
            key, value = line.split(": ", 1)  # 分割每行的键和值
            entry_dict[key] = value.strip()   # 去除空格并添加到字典中
        
        # 将字典添加到列表中
        key = entry_dict['model'] + ': ' + entry_dict['pertanyaan']
        if key not in entries:
            entries[key] = [entry_dict['Menjawab']]
        else:
            is_exist = False
            for answer in entries[key]:
                if entry_dict['Menjawab'] in answer:
                    is_exist = True
                    break
            if not is_exist:
                entries[key].append(entry_dict['Menjawab'])
    print(f'length of entries: {len(entries)}')
    
    # 将条目列表转换为 JSON 格式
    return json.dumps(entries, ensure_ascii=False, indent=4)

def process_entry(item_name, query, answer):
    translated_query = translate(query, from_lang="印尼语", to_lang="中文")
    result = call_llm_with_json(item_name=item_name, query=query)
    return {
        '机型': item_name,
        '问题': query,
        '问题（中文版）': translated_query, 
        '二级标签': result['提问标签'],
        '回答': answer
    }

def build_prompt(item_name, query, answers):
    return f"""你是一个数据标注专家，现在，我将给你一个问题和多个回答，你要判断给出的这几个回答之间是否存在冲突（比如答案不一致等），如果不是，则继续判断哪个答案更完善、更贴合问题，比如该答案完全包含了另一个答案的内容
每个答案占一行，每个答案前面有答案序号，在给出哪个答案更好时，仅需要给出答案序号即可
你的回复必须严格按照json格式给出，例如{{'答案是否存在冲突': '否', '哪个答案最好': '1'}}
仅给出上述的json即可，不要复述query，不要输出额外的东西
【产品】: {item_name}
【query】: {query}
【答案】: {answers}
【回答】: 
"""

# 主程序
if __name__ == "__main__":
    # 指定您的 TXT 文件路径
    file_path = '印尼FAQ1.txt'  # 请将此替换为您的文件路径
    
    # 读取文件数据
    data = read_txt_file(file_path)
    
    # 转换为 JSON
    faq = convert_to_json(data)
    
    # 可选：将 JSON 输出写入到文件
    with open('all_faq.json', 'w', encoding='utf-8') as json_file:
        json_file.write(faq)

    faq_dataset = read_json_file('all_faq.json')
    print(len(faq_dataset))
    filter_faq_dataset = {}
    chongtu_case = []

    for k, v in tqdm(faq_dataset.items()):
        if len(v) > 1:
            prompt = build_prompt(k.split(': ')[0], k.split(': ')[1], '\n'.join([str(idx) + '. ' + cur_v for idx, cur_v in enumerate(v)]))
            response = call_llm_with_json(prompt)
            response = json.loads(response)
            if response['答案是否存在冲突'] == '是':
                chongtu_case.append(k)
            else:
                best_idx = int(response['哪个答案最好'])
                filter_faq_dataset[k] = [v[best_idx]]
        else:
            filter_faq_dataset[k] = v
    
    with open('dedup_faq_dataset.json', 'w') as out:
        json.dump(filter_faq_dataset, out, indent=4)
