import logging
import time
from pathlib import Path

from docling.datamodel.base_models import InputFormat
from docling.datamodel.pipeline_options import (
    AcceleratorDevice,
    AcceleratorOptions,
    OcrMacOptions,
    PdfPipelineOptions,
)
from docling.document_converter import DocumentConverter, PdfFormatOption

_log = logging.getLogger(__name__)


class PdfParser:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        logging.basicConfig(level=logging.INFO)

    def _setup_pipeline_options(self):
        accelerator_options = AcceleratorOptions(
            num_threads=8, device=AcceleratorDevice.CPU
        )

        pipeline_options = PdfPipelineOptions()
        pipeline_options.do_ocr = True
        pipeline_options.do_table_structure = True
        pipeline_options.table_structure_options.do_cell_matching = True
        pipeline_options.accelerator_options = accelerator_options
        pipeline_options.ocr_options = OcrMacOptions()

        return pipeline_options

    def parse_and_save(self, input_path: str, output_dir: str = "scratch"):
        pipeline_options = self._setup_pipeline_options()
        input_doc_path = Path(input_path)
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        doc_converter = DocumentConverter(
            format_options={
                InputFormat.PPTX: PdfFormatOption(pipeline_options=pipeline_options)
            }
        )

        start_time = time.time()
        conv_result = doc_converter.convert(input_doc_path)
        end_time = time.time() - start_time

        self.logger.info(f"Document converted in {end_time:.2f} seconds.")

        # 保存为文本文件
        doc_filename = conv_result.input.file.stem
        output_file = output_dir / f"{doc_filename}.txt"
        with output_file.open("w", encoding="utf-8") as fp:
            fp.write(conv_result.document.export_to_text())

        self.logger.info(f"Text file saved to {output_file}")


def main():
    logging.basicConfig(level=logging.INFO)

    input_doc_path = Path('/Users/<USER>/Desktop/Xiaomi 15 sales tools 2nd - batch.pdf')

    accelerator_options = AcceleratorOptions(
        num_threads=8, device=AcceleratorDevice.CPU
    )

    pipeline_options = PdfPipelineOptions()
    pipeline_options.do_ocr = True
    pipeline_options.do_table_structure = True
    pipeline_options.table_structure_options.do_cell_matching = True
    pipeline_options.accelerator_options = accelerator_options
    pipeline_options.ocr_options = OcrMacOptions()

    doc_converter = DocumentConverter(
        format_options={
            InputFormat.PPTX: PdfFormatOption(pipeline_options=pipeline_options)
        }
    )

    start_time = time.time()
    conv_result = doc_converter.convert(input_doc_path)
    end_time = time.time() - start_time

    _log.info(f"Document converted in {end_time:.2f} seconds.")

    ## Export results
    output_dir = Path("scratch")
    output_dir.mkdir(parents=True, exist_ok=True)
    doc_filename = conv_result.input.file.stem

    # Export Text format:
    with (output_dir / f"{doc_filename}.txt").open("w", encoding="utf-8") as fp:
        fp.write(conv_result.document.export_to_text())


if __name__ == "__main__":
    # main()
    pdf_parser = PdfParser()
    pdf_parser.parse_and_save(
        input_path='/Users/<USER>/Desktop/Xiaomi 15 sales tools 2nd - batch.pdf',
        output_dir='scratch'
    )