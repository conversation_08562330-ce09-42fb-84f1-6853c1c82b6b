import pandas as pd

# 帮我写个临时代码 统计以下 '/Users/<USER>/Downloads/2025-05-07-11-40-19-国际Copilot AI问答评测集v0.1.xlsx-gpt41-hm-output (1).xlsx' 推理模型答案-印尼 列的平均字符数量

# Define the file path
# file_path = '/Users/<USER>/Downloads/2025-05-07-11-40-19-国际Copilot AI问答评测集v0.1.xlsx-gpt41-hm-output (1).xlsx'
file_path = '/Users/<USER>/Downloads/2025-05-09-16-22-01-国际Copilot AI问答评测集v0.1.xlsx-0509-hm-new-output.xlsx'
# Read the Excel file
print(f"Reading file: {file_path}")
df = pd.read_excel(file_path)

# Check if the column exists
column_name = "推理模型答案-印尼"
if column_name not in df.columns:
    print(f"Column '{column_name}' not found in the Excel file.")
    print("Available columns:", df.columns.tolist())
    exit(1)

# Calculate the average character count
# First, convert any non-string values to strings and handle NaN values
df[column_name] = df[column_name].fillna("").astype(str)

# Calculate the length of each cell in the column
char_counts = df[column_name].str.len()

# Calculate the average
avg_char_count = char_counts.mean()

# Calculate total characters and count of non-empty responses
total_chars = char_counts.sum()
non_empty_count = (char_counts > 0).sum()
total_rows = len(df)

# Print the results
print(f"\nResults for column '{column_name}':")
print(f"Total rows in file: {total_rows}")
print(f"Non-empty responses: {non_empty_count}")
print(f"Total characters: {total_chars}")
print(f"Average character count: {avg_char_count:.2f}")

# Print some additional statistics
if non_empty_count > 0:
    print(f"\nAdditional statistics:")
    print(f"Minimum character count: {char_counts.min()}")
    print(f"Maximum character count: {char_counts.max()}")
    print(f"Median character count: {char_counts.median()}")
    
    # Print distribution of character counts in ranges
    print("\nDistribution of character counts:")
    ranges = [(0, 100), (100, 200), (200, 300), (300, 400), (400, 500), (500, float('inf'))]
    for start, end in ranges:
        count = ((char_counts >= start) & (char_counts < end)).sum()
        percentage = (count / total_rows) * 100
        print(f"{start}-{end if end != float('inf') else '+'} chars: {count} responses ({percentage:.1f}%)")
