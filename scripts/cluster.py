# encoding=utf-8
# Name: cluster.py
# Author: <PERSON><PERSON><PERSON>
# Date: 2025/06/06
# Purpose: query cluster for chinese copilot query


import openpyxl
import re
import json
import torch


def main():
  path = "./国际零售Copilot数据-20250501~0611-no cover.xlsx"
  work_book = openpyxl.load_workbook(path)
  sheets = work_book.sheetnames
  sheet = work_book[sheets[0]]
  query_list = []
  for row in sheet.iter_rows():
      if row[0].value != "日期":
          query_list.append(row[14].value)
  print(query_list[:10])
  
  from sentence_transformers import SentenceTransformer
  #model = SentenceTransformer('sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2')
  model = SentenceTransformer("GanymedeNil/text2vec-large-chinese")
  sentences = query_list
  embeddings = model.encode(sentences)
  similarities = model.similarity(embeddings, embeddings)
  #是否开启randomwalk
  #similarities_ = similarities
  #for i in range(10):
  #  similarities = torch.mm(similarities, similarities_)  
  similarities = similarities.tolist()
  
  result1 = {}
  result2 = {}
  tag = [0 for _ in query_list]
  i = 0
  for e in similarities:
    print(i)
    temp = zip([item for index, item in enumerate(query_list) if index != i], [item for index, item in enumerate(e) if index != i], [index for index, item in enumerate(e) if index != i])
    temp = sorted(temp, key=lambda x:x[1], reverse=True)
    result1[query_list[i]] = [{"prob": e_[1], "sentence": e_[0]} for e_ in temp]

    if tag[i] == 0:
      if temp[0][0] in result2:
        result2[temp[0][0]].append(query_list[i])
      else:
        result2[temp[0][0]] = [query_list[i]]
      tag[i] = 1
      tag[temp[0][-1]] = 1
    i += 1
  
  fout = open("result1.json", "w", encoding="utf-8")
  fout.write(json.dumps(result1, indent=4, ensure_ascii=False) + "\n")
  fout.close()

  print(len(result2))
  temp = {}
  c = 0
  for k,v in sorted(result2.items(), key=lambda x:len(x[1]), reverse=True):
    temp[k] = v
    c = c + 1 + len(v)
  print(c)

  fout = open("result2.json", "w", encoding="utf-8")
  fout.write(json.dumps(temp, indent=4, ensure_ascii=False) + "\n")
  fout.close()


if __name__ == "__main__":
  main()
