import openpyxl
from openpyxl import Workbook
from bs4 import BeautifulSoup
import re

def process_faq():
    SPU_DICT = {
        "name": "Smartphone",
        "id": "Smartphone",
        "type": "CATEGORY",
        "items": [
            {
                "name": "Xiaomi 15 Ultra",
                "id": "SPU1017890",
                "type": "PRODUCT"
            },
            {
                "name": "Xiaomi 15",
                "id": "SPU1017606",
                "type": "PRODUCT"
            },
            {
                "name": "Xiaomi 14T Pro",
                "id": "SPU1016012",
                "type": "PRODUCT"
            },
            {
                "name": "Xiaomi 14T",
                "id": "SPU1016013",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi Note 14 Pro+ 5G",
                "id": "SPU1017063",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi Note 14 Pro 5G",
                "id": "SPU1017065",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi Note 14 5G",
                "id": "SPU1017068",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi Note 14",
                "id": "SPU1017099",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi A5",
                "id": "SPU1018426",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi 14C",
                "id": "SPU1016403",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi 13",
                "id": "SPU1015542",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi 13x",
                "id": "SPU1018989",
                "type": "PRODUCT"
            },
            {
                "name": "POCO C75",
                "id": "SPU1016404",
                "type": "PRODUCT"
            },
            {
                "name": "POCO F6",
                "id": "SPU1015783",
                "type": "PRODUCT"
            },
            {
                "name": "POCO M3 Pro 5G",
                "id": "SPU1009656",
                "type": "PRODUCT"
            },
            {
                "name": "POCO M5",
                "id": "SPU1012062",
                "type": "PRODUCT"
            },
            {
                "name": "POCO M5s",
                "id": "SPU1012418",
                "type": "PRODUCT"
            },
            {
                "name": "POCO M6",
                "id": "SPU1015940",
                "type": "PRODUCT"
            },
            {
                "name": "POCO M6 Pro",
                "id": "SPU1014772",
                "type": "PRODUCT"
            },
            {
                "name": "POCO X5 5G",
                "id": "SPU1012977",
                "type": "PRODUCT"
            },
            {
                "name": "POCO X6 5G",
                "id": "SPU1014771",
                "type": "PRODUCT"
            },
            {
                "name": "POCO X6 Pro 5G",
                "id": "SPU1014799",
                "type": "PRODUCT"
            },
            {
                "name": "POCO X7 5G",
                "id": "SPU1017481",
                "type": "PRODUCT"
            },
            {
                "name": "POCO X7 Pro 5G",
                "id": "SPU1017473",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi 10 2022",
                "id": "SPU1010418",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi 10 5G",
                "id": "SPU1011265",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi 10A",
                "id": "SPU1011056",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi 10C",
                "id": "SPU1010940",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi 12",
                "id": "SPU1013678",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi 12C",
                "id": "SPU1012945",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi 13C",
                "id": "SPU1014607",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi 9A",
                "id": "SPU1008839",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi 9C",
                "id": "SPU1008513",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi A1",
                "id": "SPU1012252",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi A2",
                "id": "SPU1013202",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi A3",
                "id": "SPU1014852",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi Note 10S",
                "id": "SPU1009413",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi Note 11",
                "id": "SPU1010924",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi Note 11 Pro 5G",
                "id": "SPU1010750",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi Note 12",
                "id": "SPU1012973",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi Note 12 Pro",
                "id": "SPU1012295",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi Note 12 Pro 5G",
                "id": "SPU1012711",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi Note 13",
                "id": "SPU1014773",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi Note 13 5G",
                "id": "SPU1014610",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi Note 13 Pro",
                "id": "SPU1014433",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi Note 13 Pro 5G",
                "id": "SPU1014608",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi Note 13 Pro+ 5G",
                "id": "SPU1014609",
                "type": "PRODUCT"
            },
            {
                "name": "Redmi Note 9 Pro",
                "id": "SPU1007782",
                "type": "PRODUCT"
            },
            {
                "name": "Xiaomi 13T",
                "id": "SPU1013973",
                "type": "PRODUCT"
            },
            {
                "name": "Xiaomi 14",
                "id": "SPU1014800",
                "type": "PRODUCT"
            }
        ]
    }

    ref_spu_list = [e["name"].lower() for e in SPU_DICT["items"]]

    path = "/Users/<USER>/Downloads/workspace/work_copilot/data/FAQ数据/国际促销员Copilot(印尼)-FAQ知识库2.0(20250618更新).xlsx"
    work_book = openpyxl.load_workbook(path)
    sheets = work_book.sheetnames
    sheet = work_book[sheets[0]]
    temp_data = {}
    for row in sheet.iter_rows():
        if row[3].value != "文章公共编号":
            if row[5].value not in temp_data:
                temp_data[row[5].value] = []
                temp_data[row[5].value].append([e.value for e in row])
            else:
                temp_data[row[5].value].append([e.value for e in row])
    work_book.close()

    print(len([k for k,v in temp_data.items() if k.lower() in ref_spu_list]))
    fout = open("/Users/<USER>/Downloads/workspace/work_copilot/data/FAQ数据/印尼FAQ2.txt", "w", encoding="utf-8")
    for k,v in temp_data.items():
        if k.lower() in ref_spu_list:
            for row in v:
                if row[6] and row[7]:
                    question = row[6]
                    soup = BeautifulSoup(row[7], "lxml")
                    answer = soup.get_text()
                    
                    if re.compile("\d+\.\s*[QT][\:\.]").search(answer):
                        qa_pairs = re.findall(r'([QT][:\.]\s*.*?)([A|J|Sebuah][:\.]\s*.*?)(?=[QT][:\.]|$)', answer)
                        for question_, answer_ in qa_pairs:
                            question_ = re.sub("^[QT][:\.]\s*", "", question_)
                            answer_ = re.sub("^[A|J|Sebuah][:\.]\s*", "", answer_)
                            if len(question_) > 0 and len(re.sub("\.\d+\.$", "", re.sub("\d+\. $", "", re.sub("\n", "", answer_)))) > 0:
                                fout.write("model: " + re.sub("\n", "", k) + "\npertanyaan: " + question_ + "\nMenjawab: " + re.sub("\.\d+\.$", "", re.sub("\d+\. $", "", re.sub("\n", "", answer_))) + "\n\n")
                    else:
                        if len(question) > 0 and len(re.sub("\.\d+\.$", "", re.sub("\d+\. $", "", re.sub("\n", "", answer)))) > 0:
                            fout.write("model: " + re.sub("\n", "", k) + "\npertanyaan: " + question + "\nMenjawab: " + re.sub("\.\d+\.$", "", re.sub("\d+\. $", "", re.sub("\n", "", answer))) + "\n\n")
        else:
            print(k)
    fout.close()


if __name__ == "__main__":
    process_faq()