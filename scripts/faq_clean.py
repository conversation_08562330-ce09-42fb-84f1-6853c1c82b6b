import re
import json
import requests
import traceback
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, as_completed

# 下面这些数据可以更改
RETRIEVAL_NUM = 20
RETRIEVAL_THRESHOLD = 0
TIMEOUT_MIFY_RETRIEVAL = 5
TIMEOUT_JSON_LLM = 5

ITEM_NAME_DATASET_ID_DICT ={
  "Xiaomi 15 Ultra": "af86d34e-1d73-4316-bd5f-0c3586b2163d",
  "Xiaomi 15": "efe4fc14-f066-4558-8005-dc090bb16613",
  "Xiaomi 14T Pro": "bc3ff2b4-016a-4ed0-add4-cd9b75a1a72f",
  "Xiaomi 14T": "26e22767-cbe6-45d6-82ba-9730a6c2bf3f",
  "Redmi Note 14 Pro+ 5G": "127d9a39-d232-4e2d-a389-2b7a2c2d0dd0",
  "Redmi Note 14 Pro 5G": "58df6e3b-65f6-47a2-b50b-8e054923b482",
  "Redmi Note 14 5G": "e39f4d28-b1e6-4233-b60f-25e4b6431eff",
  "Redmi Note 14": "ab5b1753-b1f3-4da9-9f20-e98e573f89ac",
  "Redmi A5": "c2ff0581-12f6-4b76-a56d-f5ac25891a6b",
  "Redmi 14C": "077d2f09-046d-4c27-b35b-88443175b0da",
  "Redmi 13": "8e047c34-e24f-4b0f-a853-5a99c64ab8ed"
}

def retrieve_knowledge_with_score(
            query,
            dataset_id,
            retrieval_num=RETRIEVAL_NUM,
    ):
        url = f"https://mify-be.pt.xiaomi.com/api/v1/datasets/{dataset_id}/retrieve"
        headers = {
            "Authorization": f"Bearer dataset-TsPXNbDYMXJw4a8iQng4ACch",
            "Content-Type": "application/json",
        }
        body = {
            "query": query,
            "retrieval_model": {
                "search_method": "semantic_search",
                "reranking_enable": False,
                "reranking_mode": None,
                "reranking_model": {"reranking_provider_name": "", "reranking_model_name": ""},
                "weights": None,
                "top_k": retrieval_num,
                "score_threshold_enabled": False,
                "score_threshold": None,
            },
        }
        try:
            response = requests.post(url, headers=headers, json=body)
            response_json = response.json()
            result = list()
            for record in response_json.get("records", []):
                result.append({
                    "content": record["segment"]["content"],
                    "score": record["score"]
                })
            return result
        except Exception as e:
            traceback.print_exc()
            return list()
        
def call_llm_with_json(prompt, chat_request_id="unknown"):
    url = "https://mify-be.pt.xiaomi.com/api/v1/workflows/run"
    headers = {
        "Authorization": f"Bearer app-4zj3dezOPMyTcwcF7Ee2Kk39",
        "Content-Type": "application/json",
    }
    data = {
        "inputs": {"user_prompt": prompt},
        "response_mode": "blocking",
        "user": f"chat_request_id-{chat_request_id}",
    }

    try:
        response = requests.post(url, headers=headers, data=json.dumps(data))
        if response.status_code == 200:
            result = response.json()
            try:
                return True, result["data"]["outputs"]["answer"]
            except Exception as e:
                return False, []
        else:
            return False, []
    except Exception as e:
        traceback.print_exc()
        return False, []
    
def call_llm_with_json_direct(prompt, api_key_json="app-jg7AqVfXZSVfWUB6nKNWbSHr", chat_request_id="unknown"):
    url = "https://mify-be.pt.xiaomi.com/api/v1/chat-messages"
    headers = {
        "Authorization": f"Bearer {api_key_json}",
        "Content-Type": "application/json",
    }
    data = {
        "inputs": {},
        "query": prompt,
        "response_mode": "blocking",
        "user": f"chat_request_id-{chat_request_id}",
    }

    try:
        response = requests.post(url, headers=headers, data=json.dumps(data))
        if response.status_code == 200:
            result = response.json()
            # output_dict = result["data"]["outputs"]
            return result["answer"]
    except Exception as e:
        traceback.print_exc()


def process_entry(entry, minet_intro, minet_param):
    item_name = re.sub(r'\+', 'plus', re.sub(r' ', '', entry['item_name'].lower()))
    if item_name in minet_intro:
        entry['minet_intro'] = '\n'.join(minet_intro[item_name]) if isinstance(minet_intro[item_name], list) else minet_intro[item_name]
    else:
        entry['minet_intro'] = ''
    entry['minet_param'] = '\n'.join(minet_param[item_name]) if isinstance(minet_param[item_name], list) else minet_param[item_name]
    # if entry['item_name'] in ITEM_NAME_DATASET_ID_DICT:
    #     doc_dataset_id = ITEM_NAME_DATASET_ID_DICT[entry['item_name']]
    #     entry['doc'] = retrieve_knowledge_with_score(entry['query'], doc_dataset_id)
    # else:
    #     entry['doc'] = []

    # web_search_query = f"Tentang {entry['item_name']}, {entry['query']}"
    # web_search_res = call_llm_with_json(web_search_query)
    # if web_search_res[0]:
    #     entry['web_search'] = web_search_res[1]
    # else:
    #     entry['web_search'] = ''
    assert isinstance(entry['minet_intro'], str)
    assert isinstance(entry['minet_param'], str)
    # assert isinstance(entry['doc'], list)
    # assert isinstance(entry['web_search'], str)
    return entry

def build_prompt(document, item_name, query, answers):
    return f"""你是一个数据标注专家，现在，我将给你多个文档，你要先仔细阅读这些文档，然后会给你一个问题和对应的答案，你要判断根据这些文档，答案的内容是否是正确的、与文档中的信息是否存在冲突，如果没有在文档中找到答案的内容，则回复“未知”，否则答案“是”或“否”，你需要先分析原因，再给出答案
你的回复必须严格按照json格式给出，例如{{'原因': '具体原因', '答案是否存在冲突': '否'}}
仅给出上述的json即可，不要复述query，不要输出额外的东西
【文档】: {document}
【产品】: {item_name}
【query】: {query}
【答案】: {answers}
【回复】: 
"""

def process_one_faq(entry):
    documents = f"<米网介绍数据>\n{entry['minet_intro']}\n</米网介绍数据>\n\n<米网参数数据>\n{entry['minet_param']}\n</米网参数数据>"
    prompt = build_prompt(documents, entry['item_name'], entry['query'], entry['answer'])
    response = call_llm_with_json_direct(prompt)
    if response is not None:
        response_json = None
        while response_json is None:
            try:
                response_json = json.loads(response)
            except Exception as e:
                response_json = None
        entry['原因'] = response_json['原因'] if '原因' in response_json else '无'
        entry['答案是否存在冲突'] = response_json['答案是否存在冲突'] if '答案是否存在冲突' in response_json else '未知'
    else:
        entry['原因'] = '无'
        entry['答案是否存在冲突'] = '未知'
    return entry

def main():
    '''
    with open('./config/minet_intro.json', 'r') as fin:
        minet_intro = json.load(fin)

    with open('./config/minet_param.json', 'r') as fin:
        minet_param = json.load(fin)

    with open('data/dedup_faq_dataset.json', 'r') as fin:
        faq_dataset = json.load(fin)

    qa_list = []
    for idx, (k, v) in enumerate(faq_dataset.items()):
        qa_list.append(
            {
                'item_name': k.split(': ')[0],
                'query': k.split(': ')[1],
                'answer': v[0]
            }
        )
    
    results = []
    with ThreadPoolExecutor(max_workers=24) as executor:
        # 使用tqdm显示进度
        future_to_record = {
            executor.submit(process_entry, record, minet_intro, minet_param): record
            for record in qa_list
        }

        for future in tqdm(as_completed(future_to_record), total=len(future_to_record), desc="Processing"):
            record = future_to_record[future]
            try:
                result = future.result()
                if result:
                    results.append(result)
            except Exception as e:
                traceback.print_exc()
                print(f"处理记录时发生异常: {e}, 记录: {record}")
    
    with open('./data/faq_w_minet_intro_and_param.json', 'w', encoding='utf-8') as out:
        json.dump(results, out, indent=4, ensure_ascii=False)
    '''
    with open('./data/faq_w_minet_intro_and_param.json', 'r') as fin:
        faq_dataset = json.load(fin)
    
    results = []
    with ThreadPoolExecutor(max_workers=24) as executor:
        # 使用tqdm显示进度
        future_to_record = {
            executor.submit(process_one_faq, record): record
            for record in faq_dataset
        }

        for future in tqdm(as_completed(future_to_record), total=len(future_to_record), desc="Processing"):
            record = future_to_record[future]
            try:
                result = future.result()
                if result:
                    results.append(result)
            except Exception as e:
                traceback.print_exc()
                print(f"处理记录时发生异常: {e}, 记录: {record}")
    with open('./data/faq_is_conflict.json', 'w', encoding='utf-8') as out:
        json.dump(results, out, indent=4, ensure_ascii=False)
    
if __name__ == "__main__":
    main()
