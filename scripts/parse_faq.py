import os
import re
import json
import time
import traceback
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
from pathlib import Path

from fastapi import HTTPException
from loguru import logger
from tqdm import tqdm

import pandas as pd
from bs4 import BeautifulSoup
from bs4.element import NavigableString, Tag
from openai import AzureOpenAI
from openai.types.chat import ChatCompletionSystemMessageParam, ChatCompletionUserMessageParam
from openai.types.shared_params import ResponseFormatJSONObject

from service.model_manager_openai import API_VERSION, AZURE_ENDPOINT
from util.common_util import not_empty
from config.run_config import RUN_CONFIG_DICT, DATA_BASE_HOST, DATA_BASE_USER, DATA_BASE_PASSWORD, DATA_BASE_NAME, \
    DATA_BASE_PORT, REDIS_HOST, REDIS_PORT, REDIS_PASSWORD, OPENAI_API_KEY, MODEL_SOURCE, MIFY, OPENAI
from util.common_util import get_env_by_key
from core.schema.constant import TEST_ENV, ENV_NAME_KEY
from util.mysql_db_manager import DBManager
from scripts.faq_dedup import build_prompt, call_llm_with_json
from scripts.faq_clean import process_one_faq

# ToDo(hm): 定位的话，在每一个h2标签上加一个锚点就行。「 id="页面内无重复的ID"」
HTML_TEMPLATE = """\
<!DOCTYPE html>
<html lang="zh-CN">
    <body>
        <main>
            {section_html}
        </main>
    </body>
</html>
"""

SECTION_TEMPLATE = """\
<section>
    <h2>{question}</h2>
    {answer_html}
</section>
"""

env_name = get_env_by_key(ENV_NAME_KEY, TEST_ENV)
db = DBManager(
    host=RUN_CONFIG_DICT[env_name][DATA_BASE_HOST],
    user=RUN_CONFIG_DICT[env_name][DATA_BASE_USER],
    password=RUN_CONFIG_DICT[env_name][DATA_BASE_PASSWORD],
    database=RUN_CONFIG_DICT[env_name][DATA_BASE_NAME],
    port=int(RUN_CONFIG_DICT[env_name][DATA_BASE_PORT]),
    batch_size=1000
)

if not db:
    logger.error("DBManager not initialized")
    raise HTTPException(status_code=500, detail="Database manager not initialized")

def render_page(qa_list):
    section_list = list()
    for question, answer_seg in qa_list:
        answer_html_list = list()
        idx = 1
        for answer_seg_type, answer_seg_content in answer_seg:
            if answer_seg_type == 'text':
                answer_html_list.append(f"<p>{answer_seg_content}</p>")
            elif answer_seg_type == 'image':
                answer_html_list.append(
                    f'<img src="{answer_seg_content}" alt="{question}_{idx}" loading="lazy" width="400" height="300">')
                idx += 1
        answer_html = '\n'.join(answer_html_list)
        section = SECTION_TEMPLATE.format(question=question, answer_html=answer_html)
        section_list.append(section)
    section_html = '\n'.join(section_list)
    html = HTML_TEMPLATE.format(section_html=section_html)
    return html


def get_faq_record(item_name, doc_type, title, content_list, idx, raw_html, is_deleted):
    raw_data = {
        'article_public_idx': idx,
        'item_name': item_name,
        'title': title,
        'raw_html': raw_html
    }
    raw_data = json.dumps(raw_data)
    content_list = json.dumps(content_list) if not isinstance(content_list, str) else content_list

    record = {
        "item_name": item_name,
        "doc_type": doc_type,
        "title": title,
        "content_list": content_list,
        "raw_data": raw_data,
        "is_deleted": is_deleted
    }
    return record


def insert_faq_record(record):
    try:
        db.insert_record("doc_trace_source", record)
    except Exception as e:
        raise f"fail to insert_faq_record_to_db: {traceback.format_exc()}"


def extract_text_and_images_safest(html_content):
    """
    最安全的版本：完全避免提取HTML标签内容
    """
    if not html_content:
        return []

    # 使用lxml解析器（如果可用）会更准确，否则使用html.parser
    try:
        soup = BeautifulSoup(str(html_content), 'lxml')
    except:
        soup = BeautifulSoup(str(html_content), 'html.parser')

    # 移除不需要的标签及其内容
    for tag in soup(['table', 'script', 'style', 'noscript', 'code']):
        tag.decompose()

    results = []

    def process_element(element, accumulated_text=[]):
        """递归处理元素，确保正确提取文本和图片"""
        if isinstance(element, NavigableString):
            # 只处理直接的文本内容
            text = str(element).strip()
            if text:
                # 清理文本
                text = ' '.join(text.split())
                accumulated_text.append(text)

        elif isinstance(element, Tag):
            if element.name == 'img':
                # 先输出之前累积的文本
                if accumulated_text:
                    merged = ' '.join(accumulated_text)
                    if merged:
                        results.append(('text', merged))
                    accumulated_text.clear()

                # 添加图片
                src = element.get('src', '')
                if src:
                    results.append(('image', src))

            elif element.name in ['br', 'p', 'div', 'li']:
                # 这些标签可能表示文本段落的分隔
                if accumulated_text:
                    merged = ' '.join(accumulated_text)
                    if merged:
                        results.append(('text', merged))
                    accumulated_text.clear()

                # 继续处理子元素
                for child in element.children:
                    process_element(child, accumulated_text)

                # 处理完子元素后，如果有累积的文本，添加它
                if accumulated_text:
                    merged = ' '.join(accumulated_text)
                    if merged:
                        results.append(('text', merged))
                    accumulated_text.clear()

            else:
                # 其他标签，继续处理子元素
                for child in element.children:
                    process_element(child, accumulated_text)

    # 从根元素开始处理
    accumulated = []
    for child in soup.children:
        process_element(child, accumulated)

    # 处理最后可能剩余的文本
    if accumulated:
        merged = ' '.join(accumulated)
        if merged:
            results.append(('text', merged))

    return results


def load_dataset(infile):
    with open(infile, 'r', encoding='utf-8') as f:
        dataset = json.load(f)
    return dataset


def save_dataset(dataset, save_path):
    with open(save_path, 'w', encoding='utf-8') as out:
        json.dump(dataset, out, ensure_ascii=False, indent=4)
    return save_path


def process_excel_with_metadata(excel_path, sheet_name, minet_intro, minet_param, content_column='内容', output_base_dir='tmp', save_path='', chongtu_case_path=''):
    # 创建输出目录
    html_dir = Path(output_base_dir) / 'html_files'
    txt_dir = Path(output_base_dir) / 'text_files'
    html_dir.mkdir(parents=True, exist_ok=True)
    txt_dir.mkdir(parents=True, exist_ok=True)

    # 读取Excel文件
    try:
        df = pd.read_excel(excel_path, sheet_name=sheet_name)
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return

    # 检查列是否存在
    if content_column not in df.columns:
        print(f"错误：找不到列 '{content_column}'")
        return

    html_idx = 0
    # 遍历每一行
    multi_qa_article_count = 0
    multi_qa_article_with_img_count = 0
    idx_html_idx_map = {}
    entries = {}
    for idx, row in tqdm(df.iterrows()):
        try:
            idx_html_idx_map[idx] = []
            title = row["标题"]
            html_content = row[content_column]

            # 跳过空值
            if pd.isna(html_content) or not str(html_content).strip():
                continue

            # 确保是字符串
            html_content = str(html_content)

            # 提取文本和图片
            extracted_content = extract_text_and_images_safest(html_content)

            # 添加提取的内容
            qa_count = 0
            img_count = 0
            for item_type, content in extracted_content:
                if item_type == 'text':
                    if is_question_line(content):
                        qa_count += 1
                elif item_type == 'image':
                    img_count += 1
            if qa_count <= 1:
                # 单个问题文章，直接用标题作为问题
                cur_html = render_page([[title, extracted_content]])
                faq_record = get_faq_record(row.loc['SPU name'], 1, title, extracted_content, row.loc['文章公共编号'], row.loc['内容'], is_deleted=2)
                key = faq_record['item_name'] + ': ' + faq_record['title']
                if key not in entries:
                    entries[key] = []
                entries[key].append(faq_record)

                html_filename = f"{html_idx}.html"
                html_idx += 1
                html_path = html_dir / html_filename
                idx_html_idx_map[idx].append(html_idx)
                with open(html_path, 'w', encoding='utf-8') as f:
                    f.write(cur_html)
                continue

            # 处理多问题文章
            if img_count > 0:
                multi_qa_article_with_img_count += 1
                # 多问题且包含图片的手动处理就行
                # ToDo(hm): 实现这里
                # 人工查看，这些多问题带图片的faq，图片位置是可以和问题匹配上的，因此可以直接程序处理
                # continue

            # 多问题没有图片拆分就行
            multi_qa_article_count += 1
            # print(f"发现了{qa_count}个问题在一篇文章中: {html_filename}")
            cur_question = None
            cur_answer_content_list = list()
            qa_html_list = list()
            for item_type, content in extracted_content:
                if is_question_line(content):
                    if not_empty(cur_answer_content_list):
                        if not_empty(cur_question):
                            # 把之前的问题和答案加入 qa_list
                            # qa_list.append((cur_question, (cur_answer_content_list)))
                            cur_qa_html = render_page([[cur_question, cur_answer_content_list]])
                            faq_record = get_faq_record(row.loc['SPU name'], 1, cur_question, cur_answer_content_list,
                                                    row.loc['文章公共编号'], row.loc['内容'], is_deleted=2)
                            key = faq_record['item_name'] + ': ' + faq_record['title']
                            if key not in entries:
                                entries[key] = []
                            entries[key].append(faq_record)

                            qa_html_list.append(cur_qa_html)
                            cur_answer_content_list.clear()
                    cur_question = content
                    continue

                cur_answer_content_list.append(("text", content))
            if not_empty(cur_answer_content_list):
                # 处理尾巴
                if not_empty(cur_question):
                    # qa_list.append((cur_question, '\n'.join(cur_answer_content_list)))
                    cur_qa_html = render_page([[cur_question, cur_answer_content_list]])
                    faq_record = get_faq_record(row.loc['SPU name'], 1, cur_question, cur_answer_content_list, row.loc['文章公共编号'],
                                            row.loc['内容'], is_deleted=2)
                    key = faq_record['item_name'] + ': ' + faq_record['title']
                    if key not in entries:
                        entries[key] = []
                    entries[key].append(faq_record)

                    qa_html_list.append(cur_qa_html)
                    cur_answer_content_list.clear()
            for qa_html in qa_html_list:
                cur_html_filename = f"{html_idx}_.html"
                html_idx += 1
                cur_html_path = html_dir / cur_html_filename
                idx_html_idx_map[idx].append(html_idx)
                with open(cur_html_path, 'w', encoding='utf-8') as f:
                    f.write(qa_html)

        except Exception as e:
            import traceback
            print(f"处理第 {idx + 1} 行时出错 {e}: {traceback.format_exc()}")

    # assert len(idx_html_idx_map) == len(df), f"{len(idx_html_idx_map)}, {len(df)}"
    with open(os.path.join(output_base_dir, "idx_html_idx_map.json"), 'w', encoding='utf-8') as out:
        json.dump(idx_html_idx_map, out, indent=4, ensure_ascii=False)
    print(f"共发现{multi_qa_article_count}篇文章包含多个问题")
    print(f"共发现{multi_qa_article_with_img_count}篇文章包含多个问题和图片")
    for k, v in idx_html_idx_map.items():
        if len(v) <= 0:
            print(f"未处理的 idx: {k}")

    chongtu_case = []
    filter_faq_dataset = []
    for k, v in tqdm(entries.items()):
        if len(v) > 1:
            prompt = build_prompt(k.split(': ')[0], k.split(': ')[1], '\n'.join([str(idx) + '. ' + cur_v['content_list'] for idx, cur_v in enumerate(v)]))
            response = None
            while response is None:
                response = call_llm_with_json(prompt)
                if response is None:
                    time.sleep(2)
            response = json.loads(response)
            if response['答案是否存在冲突'] == '是':
                chongtu_case.append(k)
            else:
                best_idx = int(response['哪个答案最好'])
                filter_faq_dataset.append(
                    {
                        'item_name': k.split(': ')[0],
                        'query': k.split(': ')[1],
                        'answer': v[best_idx]['content_list'],
                        'record': v[best_idx]
                    }
                )
        else:
            filter_faq_dataset.append(
                {
                    'item_name': k.split(': ')[0],
                    'query': k.split(': ')[1],
                    'answer': v[-1]['content_list'],
                    'record': v[-1]
                }
            )

        item_name = re.sub(r'\+', 'plus', re.sub(r' ', '', filter_faq_dataset[-1]['item_name'].lower()))
        if item_name in minet_intro:
            filter_faq_dataset[-1]['minet_intro'] = '\n'.join(minet_intro[item_name]) if isinstance(minet_intro[item_name],
                                                                                   list) else minet_intro[item_name]
        else:
            filter_faq_dataset[-1]['minet_intro'] = ''
        if item_name in minet_param:
            filter_faq_dataset[-1]['minet_param'] = '\n'.join(minet_param[item_name]) if isinstance(minet_param[item_name], list) else minet_param[item_name]
        else:
            filter_faq_dataset[-1]['minet_param'] = ''
        assert isinstance(filter_faq_dataset[-1]['minet_intro'], str)
        assert isinstance(filter_faq_dataset[-1]['minet_param'], str)

    save_dataset(filter_faq_dataset, save_path)
    print(f"冲突的case个数为：{len(chongtu_case)}")
    save_dataset(chongtu_case, chongtu_case_path)
    return filter_faq_dataset


def step_two(dataset, save_chongtu_case_path):
    chongtu_case = []

    with ThreadPoolExecutor(max_workers=24) as executor:
        # 使用tqdm显示进度
        future_to_record = {
            executor.submit(process_one_faq, data): data
            for data in dataset
        }

        for future in tqdm(as_completed(future_to_record), total=len(future_to_record), desc="Processing"):
            record = future_to_record[future]
            try:
                result = future.result()
                if result:
                    if result['答案是否存在冲突'] == '是':
                        chongtu_case.append(result)
                    else:
                        cur_record = result['record']
                        cur_record['doc_key'] = json.loads(cur_record['raw_data'])['article_public_idx']
                        insert_faq_record(cur_record)
            except Exception as e:
                traceback.print_exc()
                print(f"处理记录时发生异常: {e}, 记录: {record}")
    print(f"step 2 冲突个数: {len(chongtu_case)}")
    save_dataset(chongtu_case, save_chongtu_case_path)
    time.sleep(50)

def is_question_line(content):
    if content.endswith("?"):
        return True
    pattern = r'^\d+\.\s?[TQ]:\s?.*$'
    if bool(re.match(pattern, content)):
        return True
    head = content[:10]
    if "Q" in head and ":" in head:
        return True
    return False


if __name__ == "__main__":
    with open('/Users/<USER>/projects/inference/config/minet_intro.json', 'r') as fin:
        minet_intro = json.load(fin)

    with open('/Users/<USER>/projects/inference/config/minet_param.json', 'r') as fin:
        minet_param = json.load(fin)

    # path = "/Users/<USER>/projects/offline_codes/data/国际促销员Copilot(印尼)-FAQ知识库2.0(20250618更新).xlsx"
    # sheet_name = "match"
    # output_dir = "/Users/<USER>/projects/offline_codes/data"
    # filter_faq_dataset_save_path = os.path.join(output_dir, 'filter_faq_dataset.json')
    # chongtu_case_path = os.path.join(output_dir, 'chongtu_case.json')
    # step_two_chongtu_case_path = os.path.join(output_dir, 'step_two_chongtu_case.json')

    path = "/Users/<USER>/Downloads/印尼地区平板SPU清单.xlsx"
    sheet_name = "FAQ"
    output_dir = "/Users/<USER>/Downloads"
    filter_faq_dataset_save_path = os.path.join(output_dir, 'filter_faq_dataset.json')
    chongtu_case_path = os.path.join(output_dir, 'chongtu_case.json')
    step_two_chongtu_case_path = os.path.join(output_dir, 'step_two_chongtu_case.json')

    # 处理原始 html 内容并保存为 txt 文件
    '''
    # step 1: parse faq + 整句去重
    process_excel_with_metadata(path, sheet_name=sheet_name, minet_intro=minet_intro, minet_param=minet_param, output_base_dir=output_dir, save_path=filter_faq_dataset_save_path, chongtu_case_path=chongtu_case_path)
    time.sleep(10)
    '''
    # step 2: 与米网介绍和参数是否存在冲突 + 写入数据库
    filter_faq_dataset = load_dataset(filter_faq_dataset_save_path)
    step_two(filter_faq_dataset, step_two_chongtu_case_path)