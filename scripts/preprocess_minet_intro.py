import json

from core.processor import normalize_item_name


def filter_content(raw_content, item_name):
    split_lines = raw_content.split('\n')
    result_list = list()
    normalized = normalize_item_name(item_name)
    for line in split_lines:
        if line.startswith("*"):
            continue

        striped = line.strip()
        if len(striped) == 0:
            continue

        if striped == item_name or normalized == normalize_item_name(striped):
            continue

        result_list.append(striped)
    return result_list

if __name__ == '__main__':
    # minet_raw_data.json 中的数据是通过 coze 爬取的
    # https://www.coze.cn/store/agent/7408853092592173094?bid=6gi993pf8401k&from=store_search_suggestion
    path = "/config/minet_raw_data.json"
    name_content_dict = dict()
    with open(path) as fin:
        raw_data = json.load(fin)
        for cur_info in raw_data:
            output_data = cur_info["output"][0]["data"]
            item_name = output_data["title"]
            content = output_data["content"]
            filtered_content = filter_content(content, item_name)
            normalized_name = normalize_item_name(item_name)
            name_content_dict[normalized_name] = filtered_content
    output_path = "/config/minet_intro.json"
    with open(output_path, "w") as fout:
        fout.write(json.dumps(name_content_dict, indent=2, ensure_ascii=False))
