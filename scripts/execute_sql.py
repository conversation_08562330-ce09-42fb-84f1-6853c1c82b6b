import time
import queue
import pymysql
import threading
import traceback
from loguru import logger

from typing import List, Tuple, Optional, Dict, Any
from dbutils.pooled_db import PooledDB
from util.feishu_util import feishu_robot_send_msg


class SingletonMeta(type):
    """
    线程安全的单例模式元类。
    """
    _instances = {}
    _lock: threading.Lock = threading.Lock()

    def __call__(cls, *args, **kwargs):
        # 双重检查锁定
        if cls not in cls._instances:
            with cls._lock:
                if cls not in cls._instances:
                    instance = super().__call__(*args, **kwargs)
                    cls._instances[cls] = instance
        return cls._instances[cls]


class DBManager(metaclass=SingletonMeta):
    def __init__(self, host: str, user: str, password: str, database: str, port: int,
                 mincached: int = 10, maxcached: int = 20, maxconnections: int = 100,
                 blocking: bool = True, maxusage: Optional[int] = None, batch_size: int = 100):
        """
        初始化数据库连接池参数，并设置批量操作和异步处理的参数。
        由于采用单例模式，只会在第一次创建实例时执行此方法。
        """
        # 防止多次初始化
        if hasattr(self, '_initialized') and self._initialized:
            return

        self.host = host
        self.user = user
        self.password = password
        self.database = database
        self.port = port
        self.pool: Optional[PooledDB] = None
        self.init_pool(
            mincached=mincached,
            maxcached=maxcached,
            maxconnections=maxconnections,
            blocking=blocking,
            maxusage=maxusage
        )

        # 批量操作相关
        self.write_queue = queue.Queue(maxsize=10000)  # 设置最大队列大小，避免内存过高
        self.batch_size = batch_size
        self.stop_event = threading.Event()
        self.worker_thread = threading.Thread(target=self._batch_insert_worker, daemon=True)
        self.worker_thread.start()

        self._initialized = True  # 标记为已初始化

    def init_pool(self, mincached: int, maxcached: int, maxconnections: int,
                  blocking: bool, maxusage: Optional[int]):
        """初始化连接池"""
        try:
            self.pool = PooledDB(
                creator=pymysql,  # 使用 pymysql 作为连接数据库的模块
                mincached=mincached,
                maxcached=maxcached,
                maxconnections=maxconnections,
                blocking=blocking,
                maxusage=maxusage,
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database,
                port=self.port,
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor,
                autocommit=True  # 自动提交事务
            )
            logger.info("连接池初始化成功")
        except pymysql.MySQLError as e:
            logger.error(f"初始化连接池时出错: {str(e)}: {traceback.format_exc()}")
            raise e  # 重新抛出异常以便调用者处理

    def get_connection(self) -> Optional[pymysql.connections.Connection]:
        """从连接池中获取一个连接"""
        if not self.pool:
            logger.error("连接池未初始化")
            return None

        try:
            conn = self.pool.connection()
            # 检测连接是否有效
            with conn.cursor() as cursor:
                cursor.execute("SELECT 1")  # 简单的查询操作以确认连接
            return conn
        except Exception as e:
            logger.error(f"连接失效，重新初始化连接池: {e}")
            self.init_pool(self.pool._mincached, self.pool._maxcached,
                           self.pool._maxconnections, self.pool._blocking,
                           self.pool._maxusage)  # 使用已有设置重新初始化连接池
            return self.get_connection()  # 递归尝试获取新的连接

    def disconnect(self):
        """关闭连接池并停止批量插入线程"""
        if self.pool:
            self.pool.close()
            logger.info("连接池已关闭")
        self.stop_event.set()
        self.worker_thread.join()
        logger.info("批量插入线程已停止")

    def query_data(self, query: str, params: Tuple = ()) -> Optional[List[Dict[str, Any]]]:
        """
        执行查询并返回结果
        :param query: SQL 查询语句
        :param params: 查询参数（可选）
        :return: 查询结果
        """
        conn = self.get_connection()
        if not conn:
            logger.error("无法获取数据库连接")
            return None

        try:
            with conn.cursor() as cursor:
                cursor.execute(query, params)
                result = cursor.fetchall()
                return result
        except pymysql.MySQLError as e:
            logger.error(f"查询数据时出错: {e}")
            return None
        finally:
            conn.close()  # 释放连接回连接池

    def insert_data(self, insert_query: str, data: Tuple):
        """
        将写入请求放入队列，等待批量插入
        :param insert_query: 插入的 SQL 语句，使用占位符
        :param data: 插入的数据，可以是单条记录（元组）或多条记录的列表
        """
        if isinstance(data, list):
            for record in data:
                try:
                    self.write_queue.put((insert_query, record), block=True, timeout=5)
                except queue.Full:
                    logger.error("写入队列已满，无法插入数据")
        else:
            try:
                self.write_queue.put((insert_query, data), block=True, timeout=5)
            except queue.Full:
                logger.error("写入队列已满，无法插入数据")

    def insert_record(self, table: str, record: Dict[str, Any]):
        """
        将字典记录插入到指定的表中，自动构造 SQL 语句。
        :param table: 目标表名称
        :param record: 插入的数据字典
        """
        if not record:
            logger.info("插入记录为空")
            return

        columns = list(record.keys())
        placeholders = ', '.join(['%s'] * len(columns))
        columns_sql = ', '.join([f"`{col}`" for col in columns])
        insert_query = f"INSERT INTO `{table}` ({columns_sql}) VALUES ({placeholders})"
        values = tuple(record[col] for col in columns)
        self.insert_data(insert_query, values)

    def _batch_insert_worker(self):
        """后台线程，等待队列中有足够的数据时进行批量插入"""
        while not self.stop_event.is_set() or not self.write_queue.empty():
            batch = []
            # 阻塞直到获取 batch_size 个数据或队列为空
            try:
                for _ in range(self.batch_size):
                    insert_query, record = self.write_queue.get(timeout=1)  # 可设定适当的timeout
                    batch.append((insert_query, record))
            except queue.Empty:
                pass  # 如果在timeout内未获取到足够的数据，继续检查是否需要退出

            if batch:
                # 检查所有 insert_query 是否相同
                first_query = batch[0][0]
                if all(q == first_query for q, _ in batch):
                    records = [r for _, r in batch]
                    self._execute_batch_insert(first_query, records)
                else:
                    # 如果不同，逐个插入
                    for q, r in batch:
                        self._execute_batch_insert(q, [r])  # 单条插入

        # 程序退出前处理剩余的数据（如果有）
        remaining = []
        while not self.write_queue.empty():
            try:
                insert_query, record = self.write_queue.get_nowait()
                remaining.append((insert_query, record))
            except queue.Empty:
                break

        for insert_query, records in remaining:
            self._execute_batch_insert(insert_query, [records])  # 单条插入

    def _execute_batch_insert(self, insert_query: str, records: List[Tuple]):
        """执行批量插入操作，带重试机制"""
        if not records:
            return

        retries = 3
        for attempt in range(1, retries + 1):
            conn = self.get_connection()
            if not conn:
                logger.error("无法获取数据库连接，批量插入失败")
                return

            try:
                with conn.cursor() as cursor:
                    cursor.executemany(insert_query, records)
                    logger.info(f"批量插入了 {cursor.rowcount} 行数据")
                    break  # 成功则跳出重试循环
            except pymysql.MySQLError as e:
                logger.error(f"批量插入数据时出错（尝试 {attempt}/{retries}）：{e}")
                if attempt < retries:
                    logger.error("等待 2 秒后重试...")
                    time.sleep(2)
                else:
                    logger.error("所有重试尝试均失败，放弃批量插入")
                    feishu_robot_send_msg(f"写入数据库发生错误 {str(e)}: {records}")
            finally:
                conn.close()  # 释放连接回连接池


def insert_to_doc_trace_source(item_name, doc_type, title, content_list, raw_data):
    db = DBManager(
        host='gaea.test.mysql01.b2c.srv',
        user='global_salev85_wn',
        password='-wylY93MO8Gs7LMUhIQWDthX4ukl3CDl',
        database='global_sale_copilot',
        port=13306,
        batch_size=1
    )

    insert_query = """
    INSERT INTO doc_trace_source (
        item_name,
        doc_type,
        title,
        content_list,
        raw_data
    ) VALUES (%s, %s, %s, %s, %s)
    """
    db.insert_data(insert_query, (item_name, doc_type, title, content_list, raw_data))
    db.disconnect()
    print("数据已提交插入队列")

def batch_insert_to_doc_trace_source():
    pass


if __name__ == "__main__":
    insert_to_doc_trace_source("Xiaomi 15", 1, "问题", "答案", "原始数据")

    exit()
    # 初始化 DBManager
    # 测试环境
    db = DBManager(
        host='gaea.test.mysql01.b2c.srv',
        user='global_salev85_wn',
        password='-wylY93MO8Gs7LMUhIQWDthX4ukl3CDl',
        database='global_sale_copilot',
        port=13306,
        batch_size=1
    )
    '''#测试环境
    db = DBManager(
        host='gaea.test.mysql01.b2c.srv', 
        user='global_salev85_wn', 
        password='-wylY93MO8Gs7LMUhIQWDthX4ukl3CDl', 
        database='global_sale_copilot',
        port=13306,
        batch_size=1
    )
    #预发环境
    db = DBManager(
        host='cn.ai.international.global-sale-copilot.mysql.srv', 
        user='global_salev13_wn', 
        password='CBBrmkS0-S-56uN84AU4-F_IaCynNCA-', 
        database='global_sale_copilot',
        port=7020,
        batch_size=1
    )
    '''
    # #生产环境
    # db = DBManager(
    #     host='sgp.ai.international.global-sale-copilot.mysql.srv',
    #     user='global_salev67_wn',
    #     password='5IYt1TIbfxKvVa3tuwkE1lERbC66-eSh',
    #     database='global_sale_copilot',
    #     port=6946,
    #     batch_size=1
    # )
    #
    res = db.query_data("""SELECT * FROM doc_trace_source""")
    print(len(res))
    '''
    # 定义插入语句
    insert_query = """
    INSERT INTO global_copilot_product_info (
        product_name,
        product_id,
        first_key,
        second_key,
        value
    ) VALUES (%s, %s, %s, %s, %s)
    """

    # 准备单条插入数据（示例）
    single_data = (
        'Xiaomi 15',                    # conversation_id
        'SPU010101',                    # question_id
        'key1',             # question_content
        'key2',              # response
        'value',                               # answer_type
    )

    batch_data = []
    path = "/home/<USER>/workspace/work_copilot/data/双机对比参数_2025.6.10更新/"
    for src in os.listdir(path):
        if src[0] != ".":
            print(src)
            for file in os.listdir(path+src+"/"):
                matched = re.compile("^([^\.\~]*)\((.*)\)").match(file)
                if matched:
                    spu_name = matched.group(1)
                    spu_id = matched.group(2)

                    work_book = openpyxl.load_workbook(path + src + "/" + file)
                    sheets = work_book.sheetnames
                    i = 0
                    for row in work_book[sheets[0]].iter_rows():
                        if i != 0:
                            batch_data.append((spu_name, spu_id, row[3].value, row[4].value, row[5].value))
                        i += 1



    # 插入单条数据
    #db.insert_data(insert_query, single_data)
    #print("单条数据已提交插入队列")

    # 插入批量数据
    db.insert_data(insert_query, batch_data)
    print("批量数据已提交插入队列")
    db.disconnect()
    '''
