#!/usr/bin/env python3
import json

def normalize_item_name(raw_name):
    raw_name = raw_name.strip()
    # 去掉字符串中的所有空格
    no_spaces = raw_name.replace(" ", "")
    # 将+换成 plus
    replace_plus = no_spaces.replace("+", "plus")
    # 将所有字符转换为小写
    lower_case = replace_plus.lower()
    return lower_case

def main():
    # Load item_id_name.json
    with open("../config/item_id_name.json", "r") as f:
        item_id_name_data = json.load(f)

    # Load minet_param.json
    with open("../config/minet_param.json", "r") as f:
        minet_param_data = json.load(f)

    # Extract all item names from item_id_name.json and normalize them
    normalized_item_names = []
    normalized_item_names_set = set()
    for category in item_id_name_data:
        for item in category["items"]:
            item_name = item["name"]
            normalized_name = normalize_item_name(item_name)
            normalized_item_names.append((item_name, normalized_name))
            normalized_item_names_set.add(normalized_name)

    # Print a few examples of normalization
    print("Examples of normalization:")
    for i, (original_name, normalized_name) in enumerate(normalized_item_names):
        if i < 5 or "+" in original_name:  # Show first 5 and any with '+' in the name
            print(f"  - '{original_name}' -> '{normalized_name}'")

    # Check if each normalized name exists in minet_param.json
    missing_in_minet_param = []
    for original_name, normalized_name in normalized_item_names:
        if normalized_name not in minet_param_data:
            missing_in_minet_param.append((original_name, normalized_name))

    # Check if there are items in minet_param.json that are not in item_id_name.json
    minet_param_keys_set = set(minet_param_data.keys())
    missing_in_item_id_name = minet_param_keys_set - normalized_item_names_set

    # Print results for items missing in minet_param.json
    if missing_in_minet_param:
        print(f"\nFound {len(missing_in_minet_param)} items from item_id_name.json missing in minet_param.json:")
        for original_name, normalized_name in missing_in_minet_param:
            print(f"  - {original_name} (normalized: {normalized_name})")
    else:
        print("\nAll normalized item names from item_id_name.json are found in minet_param.json!")

    # Print results for items missing in item_id_name.json
    if missing_in_item_id_name:
        print(f"\nFound {len(missing_in_item_id_name)} items in minet_param.json that are not in item_id_name.json:")
        for key in sorted(missing_in_item_id_name):
            print(f"  - {key}")
    else:
        print("\nAll keys in minet_param.json are also in item_id_name.json!")

    # Print total counts for reference
    print(f"\nTotal items in item_id_name.json: {len(normalized_item_names)}")
    print(f"Total items in minet_param.json: {len(minet_param_data)}")

if __name__ == "__main__":
    main()
