import streamlit as st

# 必须是第一个 Streamlit 命令
st.set_page_config(
    page_title="单个请求",
    page_icon="💬",
    layout="wide",
)

import sys
import os

# Add the parent directory to the path so we can import from the root
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from frontend.mock_chat import mock_chat
from util.file_util import get_project_dir

# Get project directory
project_dir = get_project_dir()

# Call the mock_chat function
mock_chat(project_dir)
