import streamlit as st

# 必须是第一个 Streamlit 命令
st.set_page_config(
    page_title="历史对话",
    page_icon="📝",
    layout="wide",
)

import sys
import os

# Add the parent directory to the path so we can import from the root
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from frontend.history_chat import history_chat
from util.file_util import get_project_dir

# Get project directory
project_dir = get_project_dir()


import urllib.parse

# PDF URL
pdf_url = "https://alsgp0-fds.api.xiaomi.net/copilot-inference/Xiaomi%2014T%20%28New%20Green%20Colors%29%20-%20Refreshment%20Training%20Material%20with%20FABE%20Style.pdf"

# 方法1: 使用 iframe 直接展示
st.title("PDF 展示示例")

# 确保 URL 正确编码
pdf_url_encoded = urllib.parse.quote(pdf_url, safe=':/?#[]@!$&\'()*+,;=')

# 使用 iframe 嵌入 PDF
pdf_display = f'<iframe src="{pdf_url}" width="700" height="1000" type="application/pdf"></iframe>'
st.markdown(pdf_display, unsafe_allow_html=True)

# Call the history_chat function
history_chat(project_dir)
