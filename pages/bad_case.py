import streamlit as st

# 必须是第一个 Streamlit 命令
st.set_page_config(
    page_title="缺陷验证",
    page_icon="🔍",
    layout="wide",
)

import sys
import os

# Add the parent directory to the path so we can import from the root
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from frontend.bad_case_test import bad_case_test
from util.file_util import get_project_dir

# Get project directory
project_dir = get_project_dir()

# Call the bad_case_test function
bad_case_test(project_dir)
