import streamlit as st

# PDF URL
pdf_url = "https://alsgp0-fds.api.xiaomi.net/copilot-inference/Xiaomi%2014T%20%28New%20Green%20Colors%29%20-%20Refreshment%20Training%20Material%20with%20FABE%20Style.pdf"

# 方法1: 使用 iframe 直接展示
st.title("PDF 展示示例")

# 确保 URL 正确编码
pdf_url_encoded = urllib.parse.quote(pdf_url, safe=':/?#[]@!$&\'()*+,;=')

# 使用 iframe 嵌入 PDF
pdf_display = f'<iframe src="{pdf_url}" width="700" height="1000" type="application/pdf"></iframe>'
st.markdown(pdf_display, unsafe_allow_html=True)

# Set page configuration - 必须是第一个 Streamlit 命令
st.set_page_config(
    page_title="国际促销员 Copilot",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Display welcome message
st.title("国际促销员 Copilot")
st.markdown("""
## 欢迎使用国际促销员 Copilot

请从侧边栏或使用以下链接选择功能：
""")

col1, col2 = st.columns(2)

with col1:
    st.markdown("### 功能页面")
    st.markdown("- [单个请求](chat) - 进行单个对话请求（430版）")
    st.markdown("- [历史对话](history_chat) - 支持完整历史对话和选择机型（多轮对话版）")
    st.markdown("- [批量处理](regression) - 批量处理请求")
    st.markdown("- [缺陷验证](bad_case) - 验证已知缺陷")

with col2:
    st.markdown("### 管理页面")
    st.markdown("- [运维](ops) - 系统运维功能")
    st.markdown("- [标注](label) - 数据标注功能")

# 添加一些简单的说明
st.info("👈 请从左侧边栏选择功能，或者点击上方链接直接访问相应页面")

# 添加一个图片或者其他内容
st.success("✅ 系统已准备就绪")
