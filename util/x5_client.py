import os
import requests
import json
import hashlib
import base64
import asyncio
from typing import List, Dict, AsyncGenerator
import sseclient
from dotenv import load_dotenv
from loguru import logger
import httpx

load_dotenv()

API_URL = os.getenv("KBS_API_URL", "http://kbs-be.pre.mi.com/x5/api/ai")
APP_ID = os.getenv("KBS_APP_ID", "kfs_ai_search")
APP_SECRET = os.getenv("KBS_APP_SECRET", "15F6D810287DF1FEDBF86B6D164048F6")
X5_METHOD = os.getenv("KBS_API_X5_METHOD", "searchStreamForExternalUse")


def base64_encode(s):
    return base64.b64encode(s.encode('utf-8')).decode('utf-8')


def md5_hash(s):
    return hashlib.md5(s.encode('utf-8')).hexdigest().upper()


def x5_request_sse(url, json_data, session, extra_headers=None):
    """
    Sends a request and handles the streaming SSE response.
    This function is a generator and yields each message data as it arrives.
    """
    x5_struct = {
        "header": {
            "method": X5_METHOD,
            "appid": APP_ID,
            "sign": md5_hash(APP_ID + json.dumps(json_data) + APP_SECRET)
        },
        "body": json.dumps(json_data)
    }

    body = 'data=' + base64_encode(json.dumps(x5_struct))

    # Default headers, adding 'Accept' for SSE
    headers = {
        "Content-Type": "application/x-www-form-urlencoded",
    }

    # If there are extra headers, update the default headers
    if extra_headers:
        headers.update(extra_headers)

    logger.info(f"[Connection] Sending SSE request to {url}")
    response = session.post(url, headers=headers, data=body, stream=True)
    response.raise_for_status()  # Raise an exception for bad status codes (4xx or 5xx)

    # Initialize the SSE client with the response object
    try:
        client = sseclient.SSEClient(response)

        # Iterate through the events from the stream
        for event in client.events():
            # The server might send empty keep-alive messages
            if not event.data:
                continue

            try:
                # The data field is a JSON string, parse it
                payload = json.loads(event.data)
                # Use 'yield' to send the parsed data back to the caller
                yield payload
            except json.JSONDecodeError:
                # Handle cases where the data is not valid JSON
                logger.warning(f"[Warning] Received non-JSON data: {event.data}")
    finally:
        response.close()
        logger.info("[Connection] SSE stream closed.")


def kbs_query(data_in: List[Dict]) -> List[Dict]:
    """
    Query the KBS system using the provided dictionary input.
    This function returns a generator that yields results from the SSE stream.
    """
    res = []

    with requests.Session() as session:
        for data in data_in:
            request_payload = {
                "query": data.get("问题", ""),
                "region": "1",
                "userId": "1",
                "channel": "KBS",
                "contextRewrite": True
            }

            full_answer, doc_formatted_string, all_docs = "", "", []

            try:
                # apply any session-specific settings if needed
                for message in x5_request_sse(API_URL, request_payload, session=session):
                    # 1. complete the answer
                    answer_chunk = message.get("answer", "")
                    print(answer_chunk, end="", flush=True)
                    full_answer += answer_chunk

                    # 2. check if there are documents
                    docs_in_message = message.get("docs")
                    if docs_in_message and isinstance(docs_in_message, list):
                        all_docs.extend(docs_in_message)
                        logger.info(f"[Info] Received {len(docs_in_message)} docs in message {message}")

                    # check if the stream has ended
                    if message.get("streamEnd") is True:
                        logger.info("[INFO] Stream finished.")
                        break

                if all_docs:
                    doc_strings = [json.dumps(doc, ensure_ascii=False, indent=4) for doc in all_docs]
                    doc_formatted_string = ", \n".join(doc_strings)
                else:
                    doc_formatted_string = "[NOTICE] No documents info sent from KBS."

            except requests.exceptions.HTTPError as http_err:
                error_msg = f"[ERROR] HTTP错误: {http_err.response.status_code} - {http_err.response.text}"
                logger.error(error_msg, exc_info=True)
                full_answer = error_msg
                doc_formatted_string = "文档获取失败"
            except requests.exceptions.RequestException as req_err:
                error_msg = f"[ERROR] 网络请求错误: {req_err}"
                logger.error(error_msg, exc_info=True)
                full_answer = error_msg
                doc_formatted_string = "文档获取失败"
            except Exception as e:
                error_msg = f"[ERROR] 处理过程中发生未知错误: {e}"
                logger.error(error_msg, exc_info=True)
                full_answer = error_msg
                doc_formatted_string = "文档获取失败"

            res.append(
                {
                    **data,
                    "kbs_bots_response": full_answer,
                    "kbs_docs_info": doc_formatted_string,
                }
            )

    return res


async def x5_request_sse_async(
        url: str,
        json_data: Dict,
        extra_headers: Dict = None
) -> AsyncGenerator[Dict, None]:
    """
    Sends a request and handles the streaming SSE response.
    This function is a generator and yields each message data as it arrives.
    """
    x5_struct = {
        "header": {
            "method": X5_METHOD,
            "appid": APP_ID,
            "sign": md5_hash(APP_ID + json.dumps(json_data) + APP_SECRET)
        },
        "body": json.dumps(json_data)
    }

    # body = 'data=' + base64_encode(json.dumps(x5_struct))
    encoded_struct = base64_encode(json.dumps(x5_struct))
    post_data = {'data': encoded_struct}

    # Default headers, adding 'Accept' for SSE
    headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        "User-Agent": "python-requests/2.32.4",
    }

    # If there are extra headers, update the default headers
    if extra_headers:
        headers.update(extra_headers)

    logger.info(f"[Connection] Sending SSE request to {url}")

    async with httpx.AsyncClient(follow_redirects=True) as client:
        # 使用 client.stream 发起流式请求，并明确指定 POST
        async with client.stream(
                "POST",
                url,
                headers=headers,
                data=post_data,
                timeout=120,
        ) as response:
            response.raise_for_status()  # 检查 HTTP 状态码

            async for line in response.aiter_lines():
                if line.startswith("data:"):
                    data_line = line[5:].strip()
                    if not data_line:
                        continue
                    try:
                        payload = json.loads(data_line)
                        yield payload
                    except json.JSONDecodeError:
                        logger.warning(f"[Warning] Received non-JSON data: {data_line}")


async def process_single_query_async(data_in: Dict) -> Dict:
    """
    Asynchronously processes a single query task. Basic unit of concurrency.
    """
    request_payload = {
        "query": data_in.get("问题", ""),
        "region": "1",
        "userId": "1",
        "channel": "KBS",
        "contextRewrite": True
    }

    full_answer, doc_formatted_string, all_docs = "", "", []

    try:
        # Use the asynchronous generator loop
        logger.info(
            f"Preparing to call SSE for query '{data_in.get('问题', '')}'. Using API_URL: '{API_URL}' (Type: {type(API_URL)})")
        async for message in x5_request_sse_async(API_URL, request_payload):
            full_answer += message.get("answer", "")
            docs_in_message = message.get("docs")
            if docs_in_message and isinstance(docs_in_message, list):
                all_docs.extend(docs_in_message)
            if message.get("streamEnd"):
                break

        if all_docs:
            doc_strings = [json.dumps(doc, ensure_ascii=False, indent=4) for doc in all_docs]
            doc_formatted_string = ", \n".join(doc_strings)

    except Exception as e:
        error_msg = f"[ERROR] Query '{data_in.get('问题', '')[:20]}...' failed: {e}"
        logger.exception(error_msg, exc_info=True)
        full_answer = error_msg
        doc_formatted_string = "[NOTICE] No documents info sent from KBS."

    return {
        **data_in,
        "kbs_bots_response": full_answer,
        "kbs_docs_info": doc_formatted_string,
    }


async def kbs_query_concurrent(data_in: List[Dict]) -> List[Dict]:
    """
    [Async Rewrite] Concurrently queries the KBS system using coroutines.
    """
    # Create a list of all tasks
    tasks = [process_single_query_async(data) for data in data_in]

    # Use asyncio.gather to execute all tasks concurrently
    # return_exceptions=True allows other tasks to continue even if one fails
    logger.info(f"Starting {len(tasks)} concurrent queries...")
    results = await asyncio.gather(*tasks, return_exceptions=True)
    logger.info("All concurrent queries finished.")

    # `results` will now contain the return values or exceptions from all tasks
    processed_results = []
    for i, res in enumerate(results):
        if isinstance(res, Exception):
            # If a task raised an exception, log it and return an error message
            logger.error(f"Task for query '{data_in[i].get('问题', '')}' resulted in an exception: {res}")
            processed_results.append({
                **data_in[i],
                "kbs_bots_response": f"Task failed: {res}",
                "kbs_docs_info": ""
            })
        else:
            # Otherwise, append the successful result
            processed_results.append(res)

    return processed_results
