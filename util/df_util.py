import numpy as np
import pandas as pd


def remove_empty_columns(df):
    """
    移除DataFrame中所有值都为空的列

    空值定义为：0, '', None, NaN, 或其他被pandas认为是空的值

    Args:
        df: pandas DataFrame

    Returns:
        pandas DataFrame: 移除空列后的DataFrame
    """
    if df.empty:
        return df

    # 创建一个副本以避免修改原始数据
    df_copy = df.copy()

    # 找出需要移除的列
    columns_to_remove = []

    for col in df_copy.columns:
        # 检查列中的所有值
        col_values = df_copy[col]

        # 检查是否所有值都为空
        # 使用多种条件来判断空值
        is_all_empty = (
            col_values.isna().all() or  # 所有值都是 NaN/None
            (col_values == '').all() or  # 所有值都是空字符串
            (col_values == 0).all() or  # 所有值都是 0
            # 检查混合情况：所有值都是 NaN、空字符串、0 或 None 的组合
            col_values.apply(lambda x: pd.isna(x) or x == '' or x == 0 or x is None).all()
        )

        if is_all_empty:
            columns_to_remove.append(col)

    # 移除空列
    if columns_to_remove:
        print(f"移除空列: {columns_to_remove}")
        df_copy = df_copy.drop(columns=columns_to_remove)

    return df_copy


def put_cols_ahead(df, cols):
    remaining = [col for col in df.columns if col not in cols]
    filtered_cols = [col for col in cols if col in df.columns]
    return df[filtered_cols + remaining]


def rm_cols(df, cols):
    remaining = [col for col in df.columns if col not in cols]
    return df[remaining]


def split_dataframe(df, n_splits):
    """
    Randomly splits a DataFrame into n equal parts

    Args:
        df: pandas DataFrame
        n_splits: number of splits desired

    Returns:
        list of DataFrames
    """
    if n_splits <= 1:
        print("Number of splits must be greater than 1")
        return [df]

    # Create array of indices and shuffle them
    indices = np.arange(len(df))
    np.random.shuffle(indices)

    # Calculate the size of each split
    avg = len(df) // n_splits
    remainder = len(df) % n_splits

    # Split the shuffled indices
    splits = []
    start = 0
    for i in range(n_splits):
        # Add one extra item for splits until remainder is consumed
        if i < remainder:
            end = start + avg + 1
        else:
            end = start + avg
        splits.append(df.iloc[indices[start:end]])
        start = end

    return splits
