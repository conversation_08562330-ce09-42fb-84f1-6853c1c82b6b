import argparse
import os


def join_path(*paths):
    return str(os.path.join(*paths))


def mkdir_if_not_exists(dir_path):
    if not os.path.exists(dir_path):
        print(f"创建目录：{dir_path}")
        os.makedirs(dir_path, exist_ok=True)


def tail(file_path, n=30):
    with open(file_path, 'r') as file:
        lines = file.readlines()
        last_lines = lines[-n:]
    return "".join(last_lines)


def is_invalid_path(path):
    return path is None or not os.path.exists(path)


def is_valid_path(path):
    return not is_invalid_path(path)


def get_project_dir():
    """
    Get the project directory from command line arguments or use the current directory.

    Returns:
        str: The project directory path
    """
    # Try to get from command line arguments
    try:
        parser = argparse.ArgumentParser()
        parser.add_argument('--project_dir', type=str)
        # Parse only known args to avoid errors with Streamlit's own arguments
        args, _ = parser.parse_known_args()
        if args.project_dir:
            return args.project_dir
    except:
        pass

    # If not available from command line, use the current directory
    return os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
