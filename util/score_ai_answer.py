import json
import requests
import aiohttp

SCORE_PARSE_CONFIG_DICT = {
    "local": {
        # "api_key": "Bearer app-aUjXOUIfL1ttLAXXKARfMnyz" # deepseek-r1
        "api_key": "Bearer app-pii8AbwXr75CuLYqAiA596UP", # gpt4.1
    },
    "test": {
        # "api_key": "Bearer app-aUjXOUIfL1ttLAXXKARfMnyz", #deepseek-r1
        "api_key": "Bearer app-pii8AbwXr75CuLYqAiA596UP", # gpt4.1
    },
    "preview": {
        # "api_key": "Bearer app-Qg1SvVvMK12gLrsrFXt2SYct", # deepseek-r1
        "api_key": "Bearer app-f5T78z8zfxrviU0odoan0c6r", # gpt4.1

    },
}

# ToDo(hm): use dify-client to refine this
def score_ai_ans(SPU, Question, Answer, Answer_AI):
    # Define the URL and headers
    url = "https://mify-be.pt.xiaomi.com/api/v1/workflows/run"
    # https://mify-be.pt.xiaomi.com/api/v1
    headers = {
        "Authorization": "Bearer app-On9wfv8rU69pV768szyiuleQ",
        "Content-Type": "application/json",
    }

    # Define the data payload
    data = {
        "inputs": {
            "SPU": SPU,
            "Question": Question,
            "Answer": Answer,
            "Answer_AI": Answer_AI,
        },
        "response_mode": "blocking",
        "user": "abc-123",
    }

    # Send the POST request
    response = requests.post(url, headers=headers, data=json.dumps(data))

    # Check the response status code
    if response.status_code == 200:
        # Print the response content
        result = response.json()
        return json.loads(result['data']['outputs']['text'])
    else:
        # Print the error message
        print(f"Error: {response.status_code}, {response.text}")


async def score_ai_ans_async(session: aiohttp.ClientSession, SPU, Question, Answer, Answer_AI):
    url = "https://mify-be.pt.xiaomi.com/api/v1/workflows/run"
    headers = {
        "Authorization": "Bearer app-On9wfv8rU69pV768szyiuleQ",
        "Content-Type": "application/json",
    }
    data = {
        "inputs": {
            "SPU": SPU,
            "Question": Question,
            "Answer": Answer,
            "Answer_AI": Answer_AI,
        },
        "response_mode": "blocking",
        "user": "abc-123",
    }

    async with session.post(url, headers=headers, json=data) as response:
        if response.status == 200:
            result = await response.json()
            return json.loads(result['data']['outputs']['text'])
        else:
            print(f"Error: {response.status}, {await response.text()}")
            return {"score": 0, "reason": "Error"}

async def score_ai_ans_parse_async(session: aiohttp.ClientSession, SPU, Question, Answer, Answer_AI, Tips_info, env):
    url = "https://mify-be.pt.xiaomi.com/api/v1/workflows/run"
    headers = {
        "Authorization": SCORE_PARSE_CONFIG_DICT[env]['api_key'],
        "Content-Type": "application/json",
    }
    data = {
        "inputs": {
            "SPU": SPU,
            "Question": Question,
            "Answer": Answer,
            "Answer_AI": Answer_AI,
            "Tips_info": Tips_info,
        },
        "response_mode": "blocking",
        "user": "abc-123",
    }

    async with session.post(url, headers=headers, json=data) as response:
        if response.status == 200:
            result = await response.json()
            return json.loads(result['data']['outputs']['text'])
        else:
            print(f"Error: {response.status}, {await response.text()}")
            return {"score": 0, "reason": str(response.status)+" "+await response.text()}

if __name__ == "__main__":
    res = score_ai_ans(SPU= 'Redmi 13',
          Question='Mesinnya pakek apa kak ?',
          Answer='Mesinnya menggunakan Prosesor MediaTek Helio G91-Ultra, dengan arsitektur CPU Cortex-A55 dan Cortex-A75 serta frekuensi CPU maksimum 2,0 GHz.',
          Answer_AI='Prosesor yang digunakan oleh Redmi 13 adalah MediaTek Helio G91-Ultra dengan arsitektur octa-core.')

    print(res['score'], res['reason'])
