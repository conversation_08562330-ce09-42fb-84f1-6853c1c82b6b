# util/redis_manager.py

import redis
from loguru import logger
import traceback
import threading
from typing import Optional, Any, Dict

class SingletonMeta(type):
    """
    线程安全的单例模式元类。
    """
    _instances: Dict[Any, Any] = {}
    _lock: threading.Lock = threading.Lock()

    def __call__(cls, *args, **kwargs):
        # 双重检查锁定
        if cls not in cls._instances:
            with cls._lock:
                if cls not in cls._instances:
                    instance = super().__call__(*args, **kwargs)
                    cls._instances[cls] = instance
        return cls._instances[cls]

class RedisManager(metaclass=SingletonMeta):
    def __init__(self, host: str, port: int, password: str):
        """
        初始化 Redis 连接参数。
        """
        if hasattr(self, '_initialized') and self._initialized:
            return

        self.host = host
        self.port = port
        self.password = password
        self.redis_instance: Optional[redis.Redis] = None

        self._initialized = False  # 标记是否已初始化

    def connect(self):
        """初始化 Redis 连接"""
        if self._initialized:
            return
        try:
            self.redis_instance = redis.StrictRedis(
                host=self.host,
                port=self.port,
                password=self.password,
                decode_responses=True
            )
            self.redis_instance.ping()
            logger.info("Redis 连接成功")
            self._initialized = True
        except Exception as e:
            logger.error(f"Redis 连接失败: {e}\n{traceback.format_exc()}")
            raise e

    def set(self, key: str, value: Any, ex: Optional[int] = None) -> bool:
        """
        设置键值对
        :param key: 键
        :param value: 值
        :param ex: 过期时间（秒）
        :return: 设置是否成功
        """
        try:
            if self.redis_instance is None:
                self.connect()
            result = self.redis_instance.set(key, value, ex=ex)
            return result
        except Exception as e:
            logger.error(f"设置键值对失败: {e}\n{traceback.format_exc()}")
            return False

    def get(self, key: str) -> Optional[Any]:
        """
        获取键对应的值
        :param key: 键
        :return: 键对应的值或 None
        """
        try:
            if self.redis_instance is None:
                self.connect()
            value = self.redis_instance.get(key)
            return value
        except Exception as e:
            logger.error(f"获取键值对失败: {e}\n{traceback.format_exc()}")
            return None

    def delete(self, key: str) -> bool:
        """
        删除键
        :param key: 键
        :return: 删除是否成功
        """
        try:
            if self.redis_instance is None:
                self.connect()
            result = self.redis_instance.delete(key)
            return result > 0
        except Exception as e:
            logger.error(f"删除键失败: {e}\n{traceback.format_exc()}")
            return False

    def exists(self, key: str) -> bool:
        """
        检查键是否存在
        :param key: 键
        :return: 键是否存在
        """
        try:
            if self.redis_instance is None:
                self.connect()
            result = self.redis_instance.exists(key)
            return result > 0
        except Exception as e:
            logger.error(f"检查键存在失败: {e}\n{traceback.format_exc()}")
            return False

    def hset(self, key: str, field: str, value: Any) -> bool:
        """
        设置哈希表中的单个字段
        :param key: 哈希表名称
        :param field: 字段名称
        :param value: 字段值
        :return: 设置是否成功
        """
        try:
            if self.redis_instance is None:
                self.connect()
            result = self.redis_instance.hset(key, field, value)
            # 返回 1 表示新增字段，0 表示字段更新
            return result == 1 or result == 0
        except Exception as e:
            logger.error(f"设置哈希字段失败: {e}\n{traceback.format_exc()}")
            return False

    def hset_mapping(self, key: str, mapping: Dict[str, Any]) -> bool:
        """
        批量设置哈希表中的字段
        :param key: 哈希表名称
        :param mapping: 字段-值字典
        :return: 设置是否成功
        """
        try:
            if self.redis_instance is None:
                self.connect()
            self.redis_instance.hset(key, mapping=mapping)
            return True
        except Exception as e:
            logger.error(f"批量设置哈希字段失败: {e}\n{traceback.format_exc()}")
            return False

    def hget(self, key: str, field: str) -> Optional[Any]:
        """
        获取哈希表中指定字段的值
        :param key: 哈希表名称
        :param field: 字段名称
        :return: 字段对应的值或 None
        """
        try:
            if self.redis_instance is None:
                self.connect()
            value = self.redis_instance.hget(key, field)
            return value
        except Exception as e:
            logger.error(f"获取哈希字段失败: {e}\n{traceback.format_exc()}")
            return None

    def expire(self, key: str, seconds: int) -> bool:
        """
        设置键的过期时间
        :param key: 键
        :param seconds: 过期时间（秒）
        :return: 设置是否成功
        """
        try:
            if self.redis_instance is None:
                self.connect()
            result = self.redis_instance.expire(key, seconds)
            return result
        except Exception as e:
            logger.error(f"设置键过期时间失败: {e}\n{traceback.format_exc()}")
            return False

    def disconnect(self):
        """关闭 Redis 连接"""
        try:
            if self.redis_instance:
                self.redis_instance.close()
                logger.info("Redis 连接已关闭")
                self._initialized = False
        except Exception as e:
            logger.error(f"断开 Redis 连接失败: {e}\n{traceback.format_exc()}")
            raise e

# 示例用法
if __name__ == "__main__":
    REDIS_HOST = "ares.tj-info-ai-business-efficiency-sale-copilot.cache.srv"
    REDIS_PORT = 22122
    REDIS_PASSWORD = ""

    def main():
        # 初始化 RedisManager
        redis_manager = RedisManager(
            host=REDIS_HOST,
            port=REDIS_PORT,
            password=REDIS_PASSWORD
        )
        redis_manager.connect()  # 显式调用连接方法

        # 设置键值对
        success = redis_manager.set("test_key", "test_value", ex=60)
        if success:
            print("设置键值对成功")

        # 获取键值对
        value = redis_manager.get("test_key")
        print(f"获取到的值: {value}")

        # 删除键
        deleted = redis_manager.delete("test_key")
        if deleted:
            print("删除键成功")

        # 断开连接
        redis_manager.disconnect()

    main()