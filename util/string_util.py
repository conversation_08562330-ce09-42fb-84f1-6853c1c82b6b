import json
import re
import unicodedata

from core.schema.constant import QUOTE_SIGN


def text_to_xml(text_lines):
    xml = ""
    for line in text_lines:
        parts = line.split(": ", 1)
        if len(parts) == 2:
            tag = parts[0].strip().replace(" ", "_")
            content = parts[1].strip()
            xml += f"<{tag}>{content}</{tag}>\n"
        else:
            tag = line.strip().replace(" ", "_")
            xml += f"<{tag}></{tag}>\n"
    # 去掉最后的换行符
    return xml.strip()


def to_json_str(data, intend=None):
    if isinstance(data, dict):
        if intend is not None:
            return json.dumps(data, indent=intend, ensure_ascii=False)
        return json.dumps(data, ensure_ascii=False)

    if hasattr(data, 'to_dict') and callable(getattr(data, 'to_dict')):
        if intend is not None:
            return json.dumps(data.to_dict(), indent=intend, ensure_ascii=False)
        return json.dumps(data.to_dict(), ensure_ascii=False)

    return str(data)


def has_valid_info(text):
    if not text:
        return False

    # Letter（包含多语言）、数字、标记（为了正确支持多语言，必须将 Mark 字符视为有效）
    valid_categories = ['L', 'N', 'M']
    valid_char_count = 0

    for char in text:
        if char.isspace():
            continue

        category = unicodedata.category(char)
        if category[0] in valid_categories:
            valid_char_count += 1

    return valid_char_count >= 1


def parse_faq_regex(text):
    # 定义正则表达式模式
    question_pattern = r'<pertanyaan>(.*?)<\\pertanyaan>'
    answer_pattern = r'<Menjawab>(.*?)<\\Menjawab>'
    # 查找所有问题和答案
    questions = re.findall(question_pattern, text, re.DOTALL)
    answers = re.findall(answer_pattern, text, re.DOTALL)
    # 组合成FAQ列表
    faq_list = []
    for q, a in zip(questions, answers):
        faq_list.append((q.strip(), a.strip()))
    return faq_list


def remove_first_line_if_match(text):
    """
    判断字符串第一行是否以 %% 开头 \n 结尾，如果是则去掉第一行
    """
    if not text:
        return text

    # 查找第一个换行符的位置
    first_newline = text.find('\n')

    if first_newline != -1:
        # 获取第一行
        first_line = text[:first_newline + 1]  # 包含换行符

        # 判断是否以 %% 开头并以 \n 结尾
        if first_line.startswith(QUOTE_SIGN) and first_line.endswith('\n'):
            # 返回去掉第一行后的内容
            return text[first_newline + 1:]

    return text


if __name__ == '__main__':
    # 测试
    test_text1 = "%%这是第一行\n这是第二行\n这是第三行"
    print(remove_first_line_if_match(test_text1))
    # 输出：这是第二行
    #      这是第三行

    test_text2 = "这不是以%%开头的\n这是第二行"
    print(remove_first_line_if_match(test_text2))
    # 输出：这不是以%%开头的
    #      这是第二行


    # # 使用示例
    # faq_text = """<pertanyaan>Fungsi apa yang didukung kamera Xiaomi 15?<\pertanyaan>
    # <Menjawab>Xiaomi 15 hadir dengan berbagai fitur kamera untuk kamera belakang dan depan, memungkinkan Anda mengambil foto dan merekam video dalam berbagai gaya dan mode.Sebagai referensi Anda, fungsi kamera yang didukung Xiaomi 15 tercantum di bawah ini: 1. Kamera belakang: Dua gaya fotografi Leica (Leica Vibrant dan Leica Authentic), Mode Film, Dolby Vision, Fastshot, Sistem lensa Master, Fokus pelacakan gerakan, Pelacakan sumber, Monitor in-ear langsung, Penangkapan gerak, Potret, Panorama, mode Pro, Selang waktu, Super Night 2.0, Video ganda, Dokumen, Supermoon, Gerakan lambat, Eksposur lama, Blur, AI mempercantik, Perbaiki distorsi lensa, Filter, Mempercantik, ShootSteady, Bidikan dinamis, Timer, Gridlines, Timed burst, HDR, Tanda air khusus, Rana suara;2. Kamera depan: Dolby Vision, Potret, Video ganda, Selang waktu, Filter, Timer, AI mempercantik, Blur, Bidikan dinamis, Berjangka waktu, Rana suara.<\Menjawab>"""
    #
    # # 解析FAQ
    # faqs = parse_faq_regex(faq_text)
    #
    # # 打印结果
    # for i, faq in enumerate(faqs, 1):
    #     print(f"FAQ {i}:")
    #     print(f"Pertanyaan: {faq['pertanyaan']}")
    #     print(f"Jawaban: {faq['jawaban'][:100]}...")  # 只显示前100个字符
    #     print("-" * 50)
