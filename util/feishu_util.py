
import json
import requests
import base64


def feishu_robot_send_msg(msg):
    feishu_robot_url = 'https://open.f.mioffice.cn/open-apis/bot/v2/hook/13aeda45-29ff-4913-8d02-35958b20d71f'
    headers = {
        'Content-Type': 'application/json',
    }
    payload = {
        "msg_type": "text",
        "content": {
            "text": msg
        }
    }
    response = requests.post(feishu_robot_url, headers=headers, data=json.dumps(payload))
    if response.status_code == 200:
        print("Message sent successfully!")
    else:
        print(f"Failed to send message: {response.status_code} - {response.text}")


def feishu_robot_send_mk(mk):
    feishu_robot_url = 'https://open.f.mioffice.cn/open-apis/bot/v2/hook/13aeda45-29ff-4913-8d02-35958b20d71f'
    headers = {
        'Content-Type': 'application/json',
    }
    response = requests.post(feishu_robot_url, headers=headers, data=json.dumps(mk))
    if response.status_code == 200:
        print("Message sent successfully!")
    else:
        print(f"Failed to send message: {response.status_code} - {response.text}")

def st_gen_url(file_path):

    # 文件内容
    with open(file_path, "rb") as f:
        file_data = f.read()

    # 生成Base64编码的Data URL
    b64_data = base64.b64encode(file_data).decode("utf-8")
    href = f' data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,{b64_data} '

    mk = {
        "msg_type": "post",
        "content": {
            "post": {
                "zh_cn": {
                    "title": "评测提示完成通知",
                    "content": [
                        [
                            {
                                "tag": "text",
                                "text": "复制下载链接至浏览器打开: "
                            },
                            {
                                "tag": "a",
                                "text": "下载链接",
                                "href": href
                            }
                        ]
                    ]
                }
            }
        }
    }
    feishu_robot_send_mk(mk)
