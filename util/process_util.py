import subprocess
import sys
import time
import traceback
from datetime import datetime

import psutil

from core.schema.constant import READABLE_TIME_FORMAT
from util.date_util import get_readable_cur_time_str, get_readable_time_str


# 将被调用的函数的 print 重定向到文件中
class FilePrintRunner:
    def __init__(self, func, output_path):
        self.func = func
        self.output_path = output_path

    def __call__(self, *args, **kwargs):
        start_time = time.time()
        print(f"{get_readable_cur_time_str()} 开始运行 {self.func.__name__} 日志参见：{self.output_path}")
        old_stdout = sys.stdout
        # old_stderr = sys.stderr
        with open(self.output_path, "a", buffering=1) as fout:
            sys.stdout = fout
            # sys.stderr = fout
            try:
                result = self.func(*args, **kwargs)
            except Exception as e:
                error_str = f"{get_readable_cur_time_str()} {self.func.__name__}运行失败，报错：\n{traceback.format_exc()}"
                print(error_str)
                sys.stdout = old_stdout
                # sys.stderr = old_stderr
                raise Exception(error_str)

            elapse_str = get_readable_time_str(int((time.time() - start_time)))
            print(f"{get_readable_cur_time_str()} 结束运行 {self.func.__name__}，耗时{elapse_str}")
            sys.stdout = old_stdout
            # sys.stderr = old_stderr
            print(
                f"{get_readable_cur_time_str()} 结束运行 {self.func.__name__}，耗时{elapse_str}，日志参见：{self.output_path}")
        return result


def is_process_running(pid):
    if pid is None:
        return False

    try:
        process = psutil.Process(pid)
        return process.is_running() and process.status() != psutil.STATUS_ZOMBIE
    except psutil.NoSuchProcess:
        return False


def get_cur_process_start_time():
    cur_process = psutil.Process()
    start_time = cur_process.create_time()
    return datetime.fromtimestamp(start_time).strftime(READABLE_TIME_FORMAT)


def kill_process_and_children(pid):
    if pid is None:
        return

    try:
        parent = psutil.Process(pid)
        children = parent.children(recursive=True)
        for child in children:
            child.kill()
        parent.kill()
    except psutil.NoSuchProcess:
        pass


def get_process_info_list(keyword):
    command = f"ps aux | grep {keyword}"
    process = subprocess.run(command, shell=True, check=True, stdout=subprocess.PIPE, universal_newlines=True)
    output = process.stdout.split('\n')
    info_list = list()
    for line in output:
        if line is None or len(line) == 0 or f"grep {keyword}" in line:
            continue

        if keyword in line:
            info_list.append(line.strip())
    return info_list


def get_child_process_count(pid):
    try:
        parent = psutil.Process(pid)
        children = parent.children(recursive=True)
        return len(children)
    except psutil.NoSuchProcess:
        return 0
