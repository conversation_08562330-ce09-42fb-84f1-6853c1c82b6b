from pyhive import hive
from TCLIService.ttypes import TOperationState
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import threading
import queue
import time
import traceback
from util.common_util import get_env_by_key
from config.run_config import RUN_CONFIG_DICT, DATA_BASE_HOST

ENV_NAME = get_env_by_key("ENV_NAME", "local")

class HiveDataManager:
    def __init__(self, user,
                 host=RUN_CONFIG_DICT[ENV_NAME][DATA_BASE_HOST],
                 catalog='iceberg_zjyprc_hadoop',
                 database_coord='tmp',
                 ):
        """
        初始化Hive连接管理器
        !!! 表创建后无法立即写入数据，给用户授权需要时间, 需要等待一段时间, 具体时间不确定, 可能是1-2分钟

        Args:
            host (str): Hive服务器地址, 数据工厂地址, 默认地址
            user (str): 用户token, 需要用户自己的空间下的token, 数据工厂下的库里面可生成
            catalog (str): 目录集群名称
            database (str): 数据库名称
        """
        self.host = host
        self.user = user
        # self.catalog = catalog
        # self.database = database
        self.config = {"""proxy.engine""": "spark"}
        self.cursor = None
        self.connect()
        self.insert_queue = queue.Queue()
        self.stop_event = threading.Event()
        self.worker_thread = threading.Thread(target=self._worker, daemon=True)
        self.worker_thread.start()

    def connect(self):
        """建立Hive连接"""
        try:
            self.cursor = hive.connect(
                host=self.host,
                configuration=self.config,
                port=80,
                username=self.user
            ).cursor()
            print("成功连接到Hive服务器")
        except Exception as e:
            print(f"连接失败: {str(e)}")
            raise

    def _worker(self):
        """后台线程负责处理插入队列"""
        print("后台线程已启动，开始监听插入队列...")
        while not self.stop_event.is_set() or not self.insert_queue.empty():
            try:
                table_name, data = self.insert_queue.get(timeout=1)
                print(f"后台线程获取到插入任务: {table_name}, 数据量: {len(data)}")
                self._insert_data(table_name, data)
                self.insert_queue.task_done()
            except queue.Empty:
                continue
            except Exception as e:
                print(f"后台插入出错: {str(e)}")
                traceback.print_exc()

    def create_table(self, table_name, schema):
        """
        创建表

        Args:
            table_name (str): 表名
            schema (str): 表结构定义
        """
        create_sql = f"""
        CREATE TABLE IF NOT EXISTS {table_name} {schema}
        """
        print(f"执行创建表SQL: {create_sql}")
        self.cursor.execute(create_sql, async_=True)
        if self._poll_status():
            return True
        return False

    def add_column(self, table_name, column_name, column_type, column_comment=None):
        """
        给表添加新字段

        Args:
            table_name (str): 表名, 根据需要表名需要给出 集群+数据库+表名
            column_name (str): 字段名
            column_type (str): 字段类型
            column_comment (str, optional): 字段注释

        Returns:
            bool: 操作是否成功
        """
        # 构建添加字段的SQL
        comment_part = f" COMMENT '{column_comment}'" if column_comment else ""
        alter_sql = f"""
        ALTER TABLE {table_name} 
        ADD COLUMNS ({column_name} {column_type}{comment_part})
        """

        print(f"执行添加字段SQL: {alter_sql}")
        self.cursor.execute(alter_sql, async_=True)
        if self._poll_status():
            return True
        return False

    def _insert_data(self, table_name, data):
        """
        插入数据

        Args:
            table_name (str): 表名, 根据需要表名需要给出 集群+数据库+表名
            data (list): 要插入的数据列表，每个元素是一个字典
        """
        if not data:
            print("警告：没有数据需要插入")
            return False

        columns = list(data[0].keys())
        values = []
        for item in data:
            value_list = []
            for col in columns:
                value = item.get(col, 'NULL')
                if isinstance(value, str):
                    value = value.replace("'", "''")
                    value_list.append(f"'{value}'")
                elif value is None:
                    value_list.append('NULL')
                else:
                    value_list.append(str(value))
            values.append(f"({','.join(value_list)})")

        insert_sql = f"""
        INSERT INTO {table_name} 
        ({','.join(columns)}) 
        VALUES {','.join(values)}
        """

        print(f"执行插入数据SQL: {insert_sql}")
        try:
            self.cursor.execute(insert_sql, async_=True)
            print(f"成功执行插入SQL: {insert_sql}")
            return True
        except Exception as e:
            print(f"插入数据失败: {str(e)}")
            traceback.print_exc()
            return False
    
    def insert_data_async(self, table_name, data):
        """将插入任务放入队列"""
        self.insert_queue.put((table_name, data))

    def close(self):
        """关闭连接"""
        self.stop_event.set()
        self.worker_thread.join()
        if self.cursor:
            try:
                self.cursor.close()
                print("成功关闭连接")
            except Exception as e:
                print(f"关闭连接时出错: {str(e)}")

# 使用示例
if __name__ == "__main__":
    # 定义要插入的数据
    sample_data = [
        {
            'infer_query_zn': '这手机打游戏卡不卡？',
            'infer_answer_zn': 'Xiaomi 15搭载3nm制程的Snapdragon® 8 Elite移动平台，配备2个4.32GHz主核和6个3.53GHz性能核，配合Adreno™ GPU，可流畅运行各类大型游戏。',
            'product_id': 'Xiaomi15',
            'update_time_str': '2023-10-01 12:00:00',
        },
        {
            'infer_query_zn': '处理器AI性能怎么样？',
            'infer_answer_zn': '采用Qualcomm AI Engine，支持AI写作、语音识别、AI翻译等智能功能，AI算力较前代提升200%。',
            'product_id': 'Xiaomi15',
            'update_time_str': '2023-10-01 12:11:56',
    }
    ]

    # 创建实例
    user = '9b51c1e4ee974a4695ffe299f6bc670a'  # 需要用户自己的空间下的token
    hive_manager = HiveDataManager(user=user)
    table_name = 'iceberg_zjyprc_hadoop.tmp.sale_copilot_log_test'

    try:
        # 连接数据库
        hive_manager.connect()

        # # 创建表
        # table_schema = """
        # (
        #     infer_query_zn VARCHAR(255),
        #     infer_answer_zn VARCHAR(1000),
        #     product_id VARCHAR(50)
        # )
        # """
        # if hive_manager.create_table(table_name, table_schema):
        #     print("表创建成功")
        # else:
        #     print("表创建失败")

        # # 添加另一个字段
        # if hive_manager.add_column(
        #     table_name=table_name,
        #     column_name='sample',
        #     column_type='string',
        #     column_comment='测试字段'
        # ):
        #     print("添加字段成功")
        # else:
        #     print("添加字段失败")

        # 插入数据
        if hive_manager.insert_data(table_name, sample_data):
            print("数据插入成功")
        else:
            print("数据插入失败")

    except Exception as e:
        print(f"发生错误: {str(e)}")
    finally:
        # 关闭连接
        hive_manager.close()