from datetime import datetime, time

from core.schema.constant import READABLE_TIME_FORMAT, READABLE_TIME_FORMAT_SHORT, TIME_FORMAT


def get_readable_cur_time_str(with_date=True):
    if with_date:
        return datetime.now().strftime(format=READABLE_TIME_FORMAT)

    return datetime.now().strftime(format=READABLE_TIME_FORMAT_SHORT)


def get_cur_time_str(format=TIME_FORMAT):
    return datetime.now().strftime(format)


def get_readable_time_str(secs):
    hours, remainder = divmod(secs, 3600)
    minutes, seconds = divmod(remainder, 60)

    # 根据条件判断格式化输出
    time_str = ""
    if hours > 0:
        time_str += f"{hours}小时"
    # 如果有小时或分钟大于0则显示分钟
    if minutes > 0 or hours > 0:
        time_str += f"{minutes}分钟"
    time_str += f"{seconds}秒"
    return time_str


def is_between(from_hour, from_min, to_hour, to_min):
    # 获取当前时间
    current_time = datetime.now().time()
    start_time = time(from_hour, from_min)
    end_time = time(to_hour, to_min)
    return start_time <= current_time <= end_time
