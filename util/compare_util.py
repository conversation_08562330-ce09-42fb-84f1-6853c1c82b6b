import collections

import pandas as pd

from util.common_util import is_empty


def compare_collections(expected, actual, path=""):
    if isinstance(expected, dict) or isinstance(actual, dict):
        if not (isinstance(expected, dict) and isinstance(actual, dict)):
            return False, f"Difference at {path}: not all dict, expected: {expected}, actual: {actual}"

        for key in expected.keys():
            if key not in actual:
                return False, f"Difference at {path}/{key}: key '{key}' not found in actual json, value in expected json: {expected[key]}"

            result, diff_path = compare_collections(expected[key], actual[key], f"{path}/{key}")
            if not result:
                return result, diff_path

        for key in actual.keys():
            if key not in expected:
                return False, f"Difference at {path}/{key}: key '{key}' not found in expected json, value in actual json: {actual[key]}"

        return True, "same"

    if isinstance(expected, list) or isinstance(actual, list):
        if not (isinstance(expected, list) and isinstance(actual, list)):
            return False, f"Difference at {path}: not all list, expected: {expected}, actual: {actual}"

        if len(expected) != len(actual):
            return False, f"Difference at {path}: list length mismatch, expected json: {len(expected)}, actual json: {len(actual)}"

        for i in range(len(expected)):
            result, diff_path = compare_collections(expected[i], actual[i], f"{path}/{i}")
            if not result:
                return result, diff_path

        return True, "same"

    if pd.isna(expected) or pd.isna(actual):
        if pd.isna(expected) and pd.isna(actual):
            return True, "same"

        return False, f"Difference at {path}: value mismatch, expected json: {expected}, actual json: {actual}"

    if expected != actual:
        return False, f"Difference at {path}: value mismatch, expected json: {expected}, actual json: {actual}"
    return True, "same"


def compare_arrays_ignore_seq(expected, actual):
    if is_empty(expected):
        return False, "expected is empty"

    if is_empty(actual):
        return False, "actual is empty"

    if len(expected) != len(actual):
        return False, f"个数不同:\nexpected={len(expected)}\nactual={len(actual)}"

    expected_counter = collections.Counter(expected)
    actual_counter = collections.Counter(actual)
    return compare_collections(expected_counter, actual_counter)
