import json
import requests
import aiohttp

from config.run_config import RUN_CONFIG_DICT, DOMAIN, API_ACCESS_TOKEN, TEST_ENV
from core.schema.translation_base import TranslationRequest
from service.model_manager import ModelManager
from util.common_util import is_empty, assert_true


def translate_to_chinese(content_to_translate, env=TEST_ENV):
    url = f"http://{RUN_CONFIG_DICT[env][DOMAIN]}/api/v1/translate"
    headers = {
        "Connection": "keep-alive",
        "Content-Type": "application/json",
        "Authorization": RUN_CONFIG_DICT[env][API_ACCESS_TOKEN],
        "X-Request-ID": "streamlit",
    }

    translate_request = TranslationRequest(
        request_id="unknown",
        content=content_to_translate
    )

    response = requests.post(url, headers=headers, data=json.dumps(translate_request.to_dict()))
    if response.status_code == 200:
        result = response.json()
        return result["content"]

    raise RuntimeError(f"Error: {response.status_code}, {response.text}")


# ToDo(hm): 去掉这个，换成 translate_new
def translate(content_to_translate, from_lang="中文", to_lang="印尼语"):
    if is_empty(content_to_translate):
        return ""

    # Define the URL and headers
    url = "https://mify-be.pt.xiaomi.com/api/v1/workflows/run"
    headers = {
        "Authorization": "Bearer app-tW5Sya7MmJieHKKD12TGMQFr",
        "Content-Type": "application/json",
    }

    # Define the data payload
    data = {
        "inputs": {
            "content_to_translate": content_to_translate,
            "from_language": from_lang,
            "to_language": to_lang,
        },
        "response_mode": "blocking",
        "user": "abc-123",
    }

    # Send the POST request
    response = requests.post(url, headers=headers, data=json.dumps(data))

    # Check the response status code
    if response.status_code == 200:
        # Print the response content
        result = response.json()
        output_dict = result["data"]["outputs"]
        return output_dict["answer"]
    else:
        # Print the error message
        print(f"Error: {response.status_code}, {response.text}")


async def translate_async(session: aiohttp.ClientSession, content_to_translate, from_lang="中文", to_lang="印尼语"):
    url = "https://mify-be.pt.xiaomi.com/api/v1/workflows/run"
    headers = {
        "Authorization": "Bearer app-weeKPmRvk4oCceuviiq1mxNC",
        "Content-Type": "application/json",
    }
    data = {
        "inputs": {
            "content_to_translate": content_to_translate,
            "from_language": from_lang,
            "to_language": to_lang,
        },
        "response_mode": "blocking",
        "user": "abc-123",
    }
    async with session.post(url, headers=headers, json=data) as response:
        if response.status == 200:
            result = await response.json()
            output_dict = result["data"]["outputs"]
            return output_dict["answer"]
        else:
            print(f"Error: {response.status}, {await response.text()}")
            return str(response.status) + " " + await response.text()


def assert_true_by_llm(prompt):
    model_manager = ModelManager()
    is_success, response, total_tokens = model_manager.call_llm_sync(prompt)
    assert_true(is_success)
    print(response)
    assert_true(response == "是")


def assert_false_by_llm(prompt):
    model_manager = ModelManager()
    is_success, response, total_tokens = model_manager.call_llm_sync("", prompt)
    assert_true(is_success)
    print(response)
    assert_true(response != "是")


if __name__ == "__main__":
    print(translate_to_chinese("Apakah paket fotografi dapat meningkatkan piksel?"))
