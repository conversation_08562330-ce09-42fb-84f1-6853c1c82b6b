import time
import queue
import pymysql
import threading
import traceback
from loguru import logger

from typing import List, Tuple, Optional, Dict, Any
from dbutils.pooled_db import PooledDB
from util.feishu_util import feishu_robot_send_msg
from config.run_config import RUN_CONFIG_DICT, PRODUCT_TABLE_NAME
from util.common_util import get_env_by_key
ENV_NAME = get_env_by_key("ENV_NAME", "local")

class SingletonMeta(type):
    """
    线程安全的单例模式元类。
    """
    _instances = {}
    _lock: threading.Lock = threading.Lock()

    def __call__(cls, *args, **kwargs):
        # 双重检查锁定
        if cls not in cls._instances:
            with cls._lock:
                if cls not in cls._instances:
                    instance = super().__call__(*args, **kwargs)
                    cls._instances[cls] = instance
        return cls._instances[cls]

class DBManager(metaclass=SingletonMeta):
    def __init__(self, host: str, user: str, password: str, database: str, port: int,
                 mincached: int = 10, maxcached: int = 20, maxconnections: int = 100,
                 blocking: bool = True, maxusage: Optional[int] = None, batch_size: int = 100):
        """
        初始化数据库连接池参数，并设置批量操作和异步处理的参数。
        由于采用单例模式，只会在第一次创建实例时执行此方法。
        """
        # 防止多次初始化
        if hasattr(self, '_initialized') and self._initialized:
            return

        self.host = host
        self.user = user
        self.password = password
        self.database = database
        self.port = port
        self.pool: Optional[PooledDB] = None
        self.init_pool(
            mincached=mincached,
            maxcached=maxcached,
            maxconnections=maxconnections,
            blocking=blocking,
            maxusage=maxusage
        )
        
        # 批量操作相关
        self.write_queue = queue.Queue(maxsize=10000)  # 设置最大队列大小，避免内存过高
        self.batch_size = batch_size
        self.stop_event = threading.Event()
        self.worker_thread = threading.Thread(target=self._batch_insert_worker, daemon=True)
        self.worker_thread.start()

        self._initialized = True  # 标记为已初始化

    def init_pool(self, mincached: int, maxcached: int, maxconnections: int,
                 blocking: bool, maxusage: Optional[int]):
        """初始化连接池"""
        try:
            self.pool = PooledDB(
                creator=pymysql,  # 使用 pymysql 作为连接数据库的模块
                mincached=mincached,
                maxcached=maxcached,
                maxconnections=maxconnections,
                blocking=blocking,
                maxusage=maxusage,
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database,
                port=self.port,
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor,
                autocommit=True  # 自动提交事务
            )
            logger.info("连接池初始化成功")
        except pymysql.MySQLError as e:
            logger.error(f"初始化连接池时出错: {str(e)}: {traceback.format_exc()}")
            raise e  # 重新抛出异常以便调用者处理

    def get_connection(self) -> Optional[pymysql.connections.Connection]:
        """从连接池中获取一个连接"""
        if not self.pool:
            logger.error("连接池未初始化")
            return None

        try:
            conn = self.pool.connection()
            # 检测连接是否有效
            with conn.cursor() as cursor:
                cursor.execute("SELECT 1")  # 简单的查询操作以确认连接
            return conn
        except Exception as e:
            logger.error(f"连接失效，重新初始化连接池: {e}")
            self.init_pool(self.pool._mincached, self.pool._maxcached,
                           self.pool._maxconnections, self.pool._blocking,
                           self.pool._maxusage)  # 使用已有设置重新初始化连接池
            return self.get_connection()  # 递归尝试获取新的连接

    def disconnect(self):
        """关闭连接池并停止批量插入线程"""
        if self.pool:
            self.pool.close()
            logger.info("连接池已关闭")
        self.stop_event.set()
        self.worker_thread.join()
        logger.info("批量插入线程已停止")

    def query_data(self, query: str, params: Tuple = ()) -> Optional[List[Dict[str, Any]]]:
        """
        执行查询并返回结果
        :param query: SQL 查询语句
        :param params: 查询参数（可选）
        :return: 查询结果
        """
        conn = self.get_connection()
        if not conn:
            logger.error("无法获取数据库连接")
            return None

        try:
            with conn.cursor() as cursor:
                cursor.execute(query, params)
                result = cursor.fetchall()
                return result
        except pymysql.MySQLError as e:
            logger.error(f"查询数据时出错: {e}")
            return None
        finally:
            conn.close()  # 释放连接回连接池

    def query_data_from_product_info(self, product_name=None, product_id=None):
        conditional_str = ""
        if product_name:
            conditional_str = f"product_name = '{product_name}'"
            if product_id:
                conditional_str = conditional_str + " and " + f"product_id = '{product_id}'"
        elif product_id:
            conditional_str = f"product_id = '{product_id}'"
        select_query = f"""
        SELECT  first_key,
                second_key,
                value
        FROM {RUN_CONFIG_DICT.get(ENV_NAME).get(PRODUCT_TABLE_NAME)}
        WHERE {conditional_str}
        """
        result = self.query_data(select_query, ())
        if result != None:
            return [[row["first_key"], row["second_key"], row["value"]] for row in result]
        return result

    def insert_data(self, insert_query: str, data: Tuple):
        """
        将写入请求放入队列，等待批量插入
        :param insert_query: 插入的 SQL 语句，使用占位符
        :param data: 插入的数据，可以是单条记录（元组）或多条记录的列表
        """
        if isinstance(data, list):
            for record in data:
                try:
                    self.write_queue.put((insert_query, record), block=True, timeout=5)
                except queue.Full:
                    logger.error("写入队列已满，无法插入数据")
        else:
            try:
                self.write_queue.put((insert_query, data), block=True, timeout=5)
            except queue.Full:
                logger.error("写入队列已满，无法插入数据")

    def insert_record(self, table: str, record: Dict[str, Any]):
        """
        将字典记录插入到指定的表中，自动构造 SQL 语句。
        :param table: 目标表名称
        :param record: 插入的数据字典
        """
        if not record:
            logger.info("插入记录为空")
            return

        columns = list(record.keys())
        placeholders = ', '.join(['%s'] * len(columns))
        columns_sql = ', '.join([f"`{col}`" for col in columns])
        insert_query = f"INSERT INTO `{table}` ({columns_sql}) VALUES ({placeholders})"
        values = tuple(record[col] for col in columns)
        self.insert_data(insert_query, values)

    def _batch_insert_worker(self):
        """后台线程，等待队列中有足够的数据时进行批量插入"""
        while not self.stop_event.is_set() or not self.write_queue.empty():
            batch = []
            # 阻塞直到获取 batch_size 个数据或队列为空
            try:
                for _ in range(self.batch_size):
                    insert_query, record = self.write_queue.get(timeout=1)  # 可设定适当的timeout
                    batch.append((insert_query, record))
            except queue.Empty:
                pass  # 如果在timeout内未获取到足够的数据，继续检查是否需要退出

            if batch:
                # 检查所有 insert_query 是否相同
                first_query = batch[0][0]
                if all(q == first_query for q, _ in batch):
                    records = [r for _, r in batch]
                    self._execute_batch_insert(first_query, records)
                else:
                    # 如果不同，逐个插入
                    for q, r in batch:
                        self._execute_batch_insert(q, [r])  # 单条插入

        # 程序退出前处理剩余的数据（如果有）
        remaining = []
        while not self.write_queue.empty():
            try:
                insert_query, record = self.write_queue.get_nowait()
                remaining.append((insert_query, record))
            except queue.Empty:
                break

        for insert_query, records in remaining:
            self._execute_batch_insert(insert_query, [records])  # 单条插入

    def _execute_batch_insert(self, insert_query: str, records: List[Tuple]):
        """执行批量插入操作，带重试机制"""
        if not records:
            return

        retries = 3
        for attempt in range(1, retries + 1):
            conn = self.get_connection()
            if not conn:
                logger.error("无法获取数据库连接，批量插入失败")
                return

            try:
                with conn.cursor() as cursor:
                    cursor.executemany(insert_query, records)
                    logger.info(f"批量插入了 {cursor.rowcount} 行数据")
                    break  # 成功则跳出重试循环
            except pymysql.MySQLError as e:
                logger.error(f"批量插入数据时出错（尝试 {attempt}/{retries}）：{e}")
                if attempt < retries:
                    logger.error("等待 2 秒后重试...")
                    time.sleep(2)
                else:
                    logger.error("所有重试尝试均失败，放弃批量插入")
                    feishu_robot_send_msg(f"写入数据库发生错误 [环境: {ENV_NAME}] {str(e)}: {records}")
            finally:
                conn.close()  # 释放连接回连接池

if __name__ == "__main__":
    # 初始化 DBManager
    db = DBManager(
        host='gaea.test.mysql01.b2c.srv', 
        user='global_salev85_wn', 
        password='-wylY93MO8Gs7LMUhIQWDthX4ukl3CDl', 
        database='global_sale_copilot',
        port=13306,
        batch_size=1
    )

    # 定义插入语句
    insert_query = """
    INSERT INTO global_copilot_question_parse (
        conversation_id,
        question_id,
        question_content,
        response,
        answer_type,
        intents,
        selected_item_names,
        actual_item_names,
        item_attributes,
        model_version,
        dt,
        prompt
    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    """

    # 准备单条插入数据（示例）
    single_data = (
        'conv_12345',                    # conversation_id
        'ques_67890',                    # question_id
        '用户的具体问题内容...',             # question_content
        '这是模型的回复内容...',              # response
        1,                               # answer_type
        'intent1,intent2',                # intents
        '商品A,商品B',                      # selected_item_names
        '商品A,商品B',                      # actual_item_names
        '电池,屏幕',                        # item_attributes
        'v1.0.0',                        # model_version
        '2025-04-23',                    # dt
        '大模型的提示词...'                  # prompt
    )

    # 准备批量插入数据（示例）
    batch_data = [
        (
            'conv_12346',
            'ques_67891',
            '另一个用户的问题内容...',
            '另一个模型的回复内容...',
            1,
            'intent3,intent4',
            '商品C,商品D',
            '商品C,商品D',
            '电池,摄像头',
            'v1.0.1',
            '2025-04-23',
            '另一个大模型的提示词...'
        ),
        (
            'conv_12347',
            'ques_67892',
            '第三个用户的问题内容...',
            '第三个模型的回复内容...',
            2,
            'intent5',
            '商品E',
            '商品E',
            '屏幕',
            'v1.0.2',
            '2025-04-23',
            '第三个大模型的提示词...'
        ),
        # 可以继续添加更多记录
    ]

    # 插入单条数据
    db.insert_data(insert_query, single_data)
    print("单条数据已提交插入队列")

    # 插入批量数据
    db.insert_data(insert_query, batch_data)
    print("批量数据已提交插入队列")
    '''
    # 模拟持续写入（可根据实际需求调整或移除）
    try:
        for i in range(1000):
            new_data = (
                f'conv_{12348 + i}',
                f'ques_{67893 + i}',
                f'用户{i}的问题内容...',
                f'模型{i}的回复内容...',
                1,
                'intent1,intent2',
                f'商品{i}A,商品{i}B',
                f'商品{i}A,商品{i}B',
                '电池,屏幕',
                'v1.0.0',
                '2025-04-23',
                f'模型{i}的提示词...'
            )
            db.insert_data(insert_query, new_data)
            time.sleep(0.005)  # 模拟高频写入，每 5ms 插入一条
    except KeyboardInterrupt:
        print("停止写入")
    finally:
        db.disconnect()
    '''
    db.disconnect()
