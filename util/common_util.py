import json
import os
import re
import time
import hashlib

from loguru import logger
from config.model_config import MODEL_VERSION

from util.string_util import to_json_str


def assert_eq(expected, actual):
    if expected != actual:
        raise RuntimeError(f"expected={expected}, actual={actual}")


def assert_not_eq(expected, actual):
    if expected == actual:
        raise RuntimeError(f"expected={expected}, actual={actual}")


def assert_true(actual):
    if not actual:
        raise RuntimeError(f"expected True, actual={actual}")


def chunk_list(lst, n):
    return [lst[i:i + n] for i in range(0, len(lst), n)]


def is_empty(item):
    return item is None or len(item) == 0


def not_empty(item):
    return not is_empty(item)


def get_cur_millis():
    return int(time.time() * 1000)


def get_elapse_str(elapse_ms):
    return f"{elapse_ms} 毫秒"


def decode_sse(raw_event_str):
    if not raw_event_str.startswith("data:"):
        return dict()

    return json.loads(raw_event_str[6:])


def get_ending_message(chat_request):
    return chat_request.chat_history[-1].messages[-1]


def may_have_bad_markdown(text):
    if is_empty(text):
        return False

    markdown_pattern = r'(markdown|Markdown|MarkDown)'
    return re.search(markdown_pattern, text) or text.startswith("```")


def pprint(data):
    print("---")
    print(to_json_str(data, intend=2))
    print("---")


def get_env_by_key(key, default=None):
    val = os.environ.get(key)
    if not val:
        logger.warning(f"环境变量 {key} 未设置，将使用默认值 {default}")
        return default

    return val


def get_chat_request_key(chat_request):
    item_name = chat_request.item_name if chat_request.item_name is not None else "空"
    joined_str_message = chat_request.joined_str_message if chat_request.joined_str_message is not None else chat_request.chat_history[-1].messages[-1].content

    chat_request_key = f"item_name: {item_name}, joined_str_message: {joined_str_message}, MODEL_VERSION: {MODEL_VERSION}"
    chat_request_key = chat_request_key.encode('utf-8')
    chat_request_key = hashlib.sha256(chat_request_key).hexdigest()
    return chat_request_key

