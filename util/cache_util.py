"""
缓存工具模块 - 用于保存和加载页面配置
"""
import json
import os
from pathlib import Path
from typing import Optional, Dict, Any
from pydantic import BaseModel

from util.file_util import join_path, mkdir_if_not_exists


class RegressionTestConfig(BaseModel):
    """回归测试页面配置缓存"""
    # 环境配置
    env: str = "预发"
    
    # 数据行选择
    start_row: int = 1
    end_row: Optional[int] = None
    
    # 基本配置
    batch_size: int = 10
    val_score_flag: bool = False
    integrate_kbs: bool = False
    
    # 列选择配置
    product_str: str = "SPU"
    query_str: str = ""
    ref_ans: str = ""
    extra_columns_list: list = []
    
    # 高级配置
    timeout: int = 60
    max_retries: int = 2
    version: int = 1
    
    # 文件配置
    custom_suffix: str = "default_suffix"
    last_used_file_path: Optional[str] = None  # 新增：上次使用的文件路径


class BadCaseTestConfig(BaseModel):
    """缺陷验证页面配置缓存"""
    # 基本配置
    selected_item: str = "UNK"
    env: str = "预发"
    user_question: str = ""
    concurrent_requests: int = 5
    
    # 时间戳，用于记录最后更新时间
    last_updated: Optional[str] = None


class CacheManager:
    """缓存管理器"""
    
    def __init__(self, project_dir: str):
        self.project_dir = project_dir
        self.cache_dir = join_path(project_dir, "tmp", "cache")
        mkdir_if_not_exists(self.cache_dir)
    
    def get_cache_file_path(self, page_name: str) -> str:
        """获取缓存文件路径"""
        return join_path(self.cache_dir, f"{page_name}_config.json")
    
    def save_config(self, page_name: str, config: BaseModel) -> bool:
        """保存配置到缓存文件"""
        try:
            cache_file = self.get_cache_file_path(page_name)
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(config.model_dump(), f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存配置失败: {e}")
            return False
    
    def load_config(self, page_name: str, config_class: type) -> Optional[BaseModel]:
        """从缓存文件加载配置"""
        try:
            cache_file = self.get_cache_file_path(page_name)
            if not os.path.exists(cache_file):
                return None
            
            with open(cache_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return config_class(**data)
        except Exception as e:
            print(f"加载配置失败: {e}")
            return None
    
    def clear_config(self, page_name: str) -> bool:
        """清除指定页面的缓存配置"""
        try:
            cache_file = self.get_cache_file_path(page_name)
            if os.path.exists(cache_file):
                os.remove(cache_file)
            return True
        except Exception as e:
            print(f"清除配置失败: {e}")
            return False
    
    def list_cached_pages(self) -> list:
        """列出所有有缓存的页面"""
        try:
            cache_files = [f for f in os.listdir(self.cache_dir) if f.endswith('_config.json')]
            return [f.replace('_config.json', '') for f in cache_files]
        except Exception:
            return []


def get_default_regression_config() -> RegressionTestConfig:
    """获取回归测试的默认配置"""
    return RegressionTestConfig()


def get_default_bad_case_config() -> BadCaseTestConfig:
    """获取缺陷验证的默认配置"""
    return BadCaseTestConfig()


def merge_config_with_file_columns(config: RegressionTestConfig, available_columns: list) -> RegressionTestConfig:
    """将缓存配置与文件列信息合并，确保列选择的有效性"""
    if not available_columns:
        return config
    
    # 检查并修正产品列
    if config.product_str not in available_columns:
        if "SPU" in available_columns:
            config.product_str = "SPU"
        else:
            config.product_str = available_columns[0]
    
    # 检查并修正问题列
    if config.query_str not in available_columns:
        for col in available_columns:
            if "问题" in col and ("印尼" in col or "idn" in col.lower()):
                config.query_str = col
                break
        else:
            config.query_str = available_columns[0] if available_columns else ""
    
    # 检查并修正参考答案列
    if config.ref_ans and config.ref_ans not in available_columns:
        config.ref_ans = ""
        for col in available_columns:
            if ("人工答案" in col and "印尼" in col) or ("参考答案" in col and "印尼" in col) or (
                    "答案" in col and ("idn" in col.lower() or "印尼" in col)):
                config.ref_ans = col
                break
    
    # 检查并修正额外列
    valid_extra_columns = [col for col in config.extra_columns_list if col in available_columns]
    config.extra_columns_list = valid_extra_columns
    
    return config
