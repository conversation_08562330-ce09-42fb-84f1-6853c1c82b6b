import json

from util.llm_util import translate


if __name__ == '__main__':
    normalized_second_label_dict = {
        "Port": "接口",
        "Navigasi & Penentuan Posisi": "导航与定位",
        "Pemutaran video": "视频",
        "L5Galileo": "导航与定位",
        "Penyimpanan & RAM": "内存与存储",
        "Konektifitas jaringan": "网络",
        "Audio": "音频",
        "Baterai": "电池与充电",
        "RAM & Kapasitas Penyimpanan": "内存与存储",
        "Navigasi & positioning": "导航与定位",
        "Pemutaran audio": "音频",
        "Sistem operasi": "用户界面与系统",
        "Baterai dan Pengisian Daya": "电池与充电",
        "Baterai & Pengisian Daya": "电池与充电",
        "Layar": "屏幕",
        "Isi Paket": "包装内容",
        "Pengisian daya": "电池与充电",
        "Buka kunci": "解锁",
        "Video": "视频",
        "Baterai & Pengisian": "电池与充电",
        "Sensors": "传感器",
        "Navigasi": "导航与定位",
        "Keamanan & Otentikasi": "安全与认证",
        "Sensor sidik jari dan Tombol": "指纹传感器与按钮",
        "Jaringan dan Konektivitas": "网络",
        "Tampilan": "外观",
        "Isi kotak": "包装内容",
        "Fitur": "功能",
        "RAM dan Kapasitas Penyimpanan": "内存与存储",
        "Sistem pendinginan": "冷却系统",
        "Desain": "设计",
        "Package contents": "包装内容",
        "Performa": "性能",
        "Penyimpanan dan RAM": "内存与存储",
        "Navigasi & Pemosisian": "导航与定位",
        "Navigasi & Posisi": "导航与定位",
        "Isi kemasan": "包装内容",
        "Dimensi": "尺寸",
        "Keamanan": "安全与认证",
        "Perekaman video kamera belakang": "后置摄像头",
        "Konektivitas": "网络",
        "Kamera depan": "前置摄像头",
        "Motor getaran": "振动电机",
        "Cara membuka ponsel": "开机方法",
        "Navigasi & Deteksi Posisi": "导航与定位",
        "NFC": "NFC",
        "Jaringan & Konektivitas": "网络",
        "Baterai dan Pengisian daya": "电池与充电",
        "Baterai dan pengisian daya": "电池与充电",
        "Tahan Cipratan, Air, dan Debu": "防水与防尘",
        "Tahan Air & Debu": "防水与防尘",
        "Keamanan & Autentikasi": "安全与认证",
        "Sistem Operasi": "操作系统",
        "Jaringan Nirkabel": "网络",
        "Sistem Pendingin": "冷却系统",
        "Termasuk dalam kotak kemasan": "包装内容",
        "UI dan sistem": "用户界面与系统",
        "Sensor": "传感器",
        "Perekaman video kamera depan": "前置摄像头",
        "Tahan dari Percikan, Air, dan Debu": "防水与防尘",
        "Antarmuka pengguna dan sistem": "用户界面与系统",
        "Kamera Depan": "前置摄像头",
        "Kamera Belakang": "后置摄像头",
        "Memori": "内存与存储",
        "Fitur AI": "AI功能",
        "Chipset": "处理器",
        "Xiaomi HyperAI": "AI功能",
        "IP rating": "防水与防尘",
        "Termasuk dalam kotak": "包装内容",
        "Multimedia": "多媒体",
        "Frekuensi jaringan": "网络",
        "Kamera": "相机",
        "Keamanan&Autentikasi": "安全与认证",
        "Navigasi & Penentuan Posis": "导航与定位",
        "Kamera belakang": "后置摄像头",
        "Perekaman kamera depan": "前置摄像头",
        "Isi Kemasan": "包装内容",
        "Navigasi & Positioning": "导航与定位",
        "Prosesor": "处理器",
    }

    # flip this
    flipped_normalized_second_label_dict = dict()
    for label in normalized_second_label_dict:
        val = normalized_second_label_dict[label]
        if val not in flipped_normalized_second_label_dict:
            flipped_normalized_second_label_dict[val] = list()
        flipped_normalized_second_label_dict[val].append(label)
    print(flipped_normalized_second_label_dict)
    exit()
    with open("normalized_label_dict.json") as fin:
        normalized_dict = json.load(fin)
        normalized_label_set = set(normalized_dict.values())
        # for label in normalized_label_set:
        #     print(label)
        # print(len(normalized_label_set))
    
    path = "/Users/<USER>/workspace/inference/api/id_knowledge.json"
    with open(path) as fin:
        knowledge_dict = json.load(fin)
    label_set = set()
    label_cnt_dict = dict()
    for product_code in knowledge_dict:
        for raw_key in knowledge_dict[product_code]:
            key = raw_key.strip()
            if key not in normalized_dict:
                print(f"missing: {raw_key}")
                continue
            
            normlaized_key = normalized_dict[key]
            label_cnt_dict[normlaized_key] = (
                label_cnt_dict[normlaized_key] + 1 if normlaized_key in label_cnt_dict else 1
            )
    print(label_cnt_dict)

    # label_translate_dict = dict()
    # for label in label_set:
    #     label_translate_dict[label] = translate(label, from_lang="印尼语", to_lang="中文")
    #     print(label, label_translate_dict[label])
    #     # update
    #     with open("label_translate_dict.json", "w") as fout:
    #         json.dump(label_translate_dict, fout, ensure_ascii=False)
