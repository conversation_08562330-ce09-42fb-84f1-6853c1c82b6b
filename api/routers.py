from datetime import datetime
import traceback
import random
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Request, Header, status
import uuid
from pydantic import BaseModel
from loguru import logger
from prometheus_client import generate_latest

from fastapi.responses import StreamingResponse

from config.common_config import get_chat_service_init_param, get_chat_service_list, get_redis_manager, get_model_manager
from util.common_util import get_env_by_key, not_empty
from config.model_config import Model<PERSON>onfigKey, MODEL_CONFIG
from config.run_config import API_ACCESS_TOKEN, RUN_CONFIG_DICT
from core.schema.chat_request import ChatRequest
from core.schema.chat_round import ChatRound
from core.enum.language import Language
from core.schema.constant import CHAT, ERROR, LOCAL_ENV, TEST_ENV
from core.validator import validate_translation_request, ChatRequestValidator
from data_loader import load_competitor_map, load_item_id_name_dict

from service.model_manager import ModelManager

from prometheus_client.openmetrics.exposition import CONTENT_TYPE_LATEST
from fastapi.responses import Response

from core.processor import preprocess_request

from service.translation_service import TranslationService
from core.schema.translation_base import TranslationResponse, TranslationRequest
from util.string_util import to_json_str
from util.redis_manager import RedisManager
from service.chat_service_base import get_suggest_queries

router = APIRouter()

ENV_NAME = get_env_by_key("ENV_NAME", "local")
# PRODUCT_ID_NAME_DICT = load_product_id_name_dict("./config/product_id_name.json")
# local 用的是 test 环境模型配置
if ENV_NAME == LOCAL_ENV:
    ENV_NAME = TEST_ENV

CHAT_SERVICE_INIT_PARAM = get_chat_service_init_param(ENV_NAME)
CHAT_SERVICE_LIST = get_chat_service_list(CHAT_SERVICE_INIT_PARAM)
REDIS_MANAGER = get_redis_manager()
COMPETITOR_MAP = load_competitor_map(MODEL_CONFIG[ENV_NAME][ModelConfigKey.ITEM_NAME_COMPETITOR_MAP_PATH])
ITEM_ID_NAME_MAP = load_item_id_name_dict(MODEL_CONFIG[ENV_NAME][ModelConfigKey.ITEM_ID_NAME_XIAOMI_PATH])

class CancelRequest(BaseModel):
    request_id: str = None


class CancelResponse(BaseModel):
    request_id: str = None
    success: bool = False


class SuggestQueriesRequest(BaseModel):
    request_id: str = None
    answer_intent: Optional[str] = None
    language: Language
    chat_history: Optional[List[ChatRound]] = None
    item_id: Optional[str] = None

    def to_dict(self):
        """Convert the model to a dictionary for API requests"""
        return self.model_dump(exclude_none=True,
                               exclude={"logger", "call_consumption_dict", "task_dict", "http_request"})


class SuggestedQueriesResponse(BaseModel):
    request_id: str = None
    suggest_queries: List[str] = []
    success: bool = False


def verify_api_token(authorization: str = Header(None)):
    if not authorization:
        logger.warning("API请求缺少令牌")
        raise HTTPException(status_code=401, detail="缺少访问令牌")

    if authorization != RUN_CONFIG_DICT[ENV_NAME][API_ACCESS_TOKEN]:
        logger.warning(f"API令牌验证失败: {authorization}...")
        raise HTTPException(status_code=403, detail="访问令牌无效")

    return authorization


@router.post("/cancel", status_code=status.HTTP_200_OK, summary="取消请求", response_model=CancelResponse)
async def cancel_request(cancel_request: CancelRequest, http_request: Request,
                         redis_manager: RedisManager = Depends(get_redis_manager)):
    # 获取或生成 request_id
    request_id = http_request.headers.get("X-Request-ID", f"default-{uuid.uuid4()}")
    cur_logger = logger.bind(request_id=request_id, chat_request_id=cancel_request.request_id)
    response = CancelResponse(request_id=cancel_request.request_id, success=False)
    cur_logger.info(f"取消请求：\n{cancel_request.request_id}")
    try:
        key = f"request:{cancel_request.request_id}"
        if redis_manager.exists(key):
            # 仅更新 should_cancel 字段为 True
            success = redis_manager.hset(key, "should_cancel", "True")
            if success:
                response.success = True
                cur_logger.info(f"请求 {cancel_request.request_id} 的 should_cancel 字段已更新为 True")
            else:
                cur_logger.error(f"更新 should_cancel 字段失败: {cancel_request.request_id}")
            task_id = redis_manager.hget(key, "task_id")
            api_key = redis_manager.hget(key, "api_key")
            if not_empty(task_id):
                await ModelManager.stop_task(task_id, api_key, cancel_request.request_id)
        else:
            # 设置 should_cancel 为 True，并将 task_id 设置为空字符串
            success = redis_manager.hset_mapping(key, mapping={"should_cancel": "True", "task_id": "", "api_key": ""})
            if success:
                # 设置一个过期时间（可选）
                redis_manager.expire(key, 3600)  # 1 小时后自动过期
                response.success = True
                cur_logger.info(f"请求被取消并创建新记录: {cancel_request.request_id}")
            else:
                cur_logger.error(f"设置取消标记失败: {cancel_request.request_id}")
    except Exception as e:
        cur_logger.error(f"取消请求时发生错误: {traceback.format_exc()}")

    return response


# ToDo(hm): to support block response?
@router.post("/chat", dependencies=[Depends(verify_api_token)])
async def chat(chat_request: ChatRequest, http_request: Request):
    cur_logger = logger
    try:
        # 获取或生成 request_id
        request_id = http_request.headers.get("X-Request-ID", f"default-{uuid.uuid4()}")
        # 创建请求特定的logger实例
        cur_logger = logger.bind(request_id=request_id, chat_request_id=chat_request.request_id)
        chat_request.logger = cur_logger
        chat_request.http_request = http_request

        # 先进行参数校验
        chat_request_json = to_json_str(chat_request)
        cur_logger.info(f"收到请求：\n{chat_request_json}")
        try:
            ChatRequestValidator().validate_chat_request(chat_request)
        except HTTPException as e:
            CHAT_SERVICE_INIT_PARAM.endpoint_call_counter.labels(object=CHAT, condition=ERROR).inc()
            raise e

        if chat_request.version is None or chat_request.version == 0:
            chat_service = CHAT_SERVICE_LIST[0]
        else:
            chat_service = CHAT_SERVICE_LIST[1]
        preprocess_request(chat_request)
        # 先进行调用，等待外部接口返回首个数据或验证无错误
        # 可以增加一个超时或者预取首个数据的逻辑，确保调用过程中没有异常
        chat_agent = chat_service.chat(chat_request)
        # 尝试获取第一个数据块
        first_chunk = await chat_agent.__anext__()

        # 如果没有异常，则开始生成 Streaming Response
        async def stream_generator():
            try:
                # 首先 yield 预取的 chunk
                yield first_chunk
                # 然后继续生成后续流式数据
                async for event in chat_agent:
                    yield event
            except Exception as e:
                # 在流式传输过程中捕获异常
                error_msg = f"chat 流式传输过程中报错(chatRequestId={chat_request.request_id}): {str(e)}"
                cur_logger.error(f"{error_msg}: {traceback.format_exc()}")
                CHAT_SERVICE_INIT_PARAM.endpoint_call_counter.labels(object=CHAT, condition=ERROR).inc()
                raise RuntimeError(error_msg)

        return StreamingResponse(stream_generator(), media_type="text/event-stream")
    except Exception as e:
        cur_logger.error(f"chat 内部报错 {str(e)}: {traceback.format_exc()}")
        CHAT_SERVICE_INIT_PARAM.endpoint_call_counter.labels(object=CHAT, condition=ERROR).inc()
        raise HTTPException(
            status_code=500, detail=f'chat 内部报错(chatRequestId={chat_request.request_id}): {str(e)}'
        )


@router.post("/suggest_queries", response_model=SuggestedQueriesResponse, summary="建议用户提问的问题")
async def suggest_queries(request: SuggestQueriesRequest, http_request: Request,
                          model_manager: ModelManager = Depends(get_model_manager)):
    # 获取或生成 request_id
    request_id = http_request.headers.get("X-Request-ID", f"default-{uuid.uuid4()}")
    cur_logger = logger.bind(request_id=request_id, chat_request_id=request.request_id)
    cur_logger.info(f"suggest_queries request：{request.request_id}")
    suggest_queries_response = SuggestedQueriesResponse(request_id=request.request_id, suggest_queries=[], success=False)
    sample_size = 12

    try:
        suggest_queries = await get_suggest_queries(request, COMPETITOR_MAP, ITEM_ID_NAME_MAP, model_manager, sample_size, cur_logger)
        suggest_queries_response.success = True
        suggest_queries_response.suggest_queries = suggest_queries

    except Exception as e:
        logger.error(f"建议用户提问的问题时发生错误: {traceback.format_exc()}")
    finally:
        return suggest_queries_response


# 定义健康检查响应模型（标准化返回结构）
class HealthCheckResponse(BaseModel):
    status: str
    timestamp: str
    # 可扩展的组件状态（如数据库、缓存等）
    components: dict = {}


@router.get(
    "/",
    summary="服务健康检查（根路径）",
    description="返回服务基础状态，适合负载均衡器探活",
    response_model=HealthCheckResponse,
)
async def health_check_root():
    try:
        response = {
            "status": "OK",
            "timestamp": datetime.now().isoformat(),
            "components": {"base": "available"},  # 基础状态标识
        }
        return response
    except Exception as e:
        logger.error(f"根路径健康检查失败: {str(e)}")
        raise HTTPException(status_code=503, detail=f"服务异常：{str(e)}")


@router.get(
    "/health",
    summary="详细健康检查",
    description="返回服务及依赖组件的详细健康状态",
    response_model=HealthCheckResponse,
)
async def health_check_detailed():
    try:
        # 可扩展的检查项（参考网页1的异步检查逻辑）
        status = {
            "status": "ok",
            "timestamp": datetime.now().isoformat(),
            "components": {
                "database": "available",  # 示例：实际可添加数据库连接检查
                "cache": "available",  # 示例：缓存服务检查
            },
        }
        return status
    except Exception as e:
        logger.error(f"详细健康检查失败: {str(e)}")
        raise HTTPException(status_code=503, detail=f"服务异常：{str(e)}")


@router.get("/metrics")
async def metrics():
    return Response(content=generate_latest(), media_type=CONTENT_TYPE_LATEST)


def get_translation_service(request: Request, translation_request: TranslationRequest) -> TranslationService:
    try:
        if "X-Request-ID" in request.headers:
            request_id = request.headers["X-Request-ID"]
        else:
            request_id = f"default-{uuid.uuid4()}"

        context_logger = logger.bind(request_id=request_id, translation_request_id=translation_request.request_id)
        request.state.logger = context_logger
        model_manager = ModelManager(
            None,
            None,
            CHAT_SERVICE_INIT_PARAM.cur_config[ModelConfigKey.DATASET_KEY],
        )
        return TranslationService(
            context_logger,
            translation_request.request_id,
            CHAT_SERVICE_INIT_PARAM.cur_config[ModelConfigKey.API_KEY_TRANSLATE],
            model_manager,
        )
    except Exception as e:
        logger.error(f"构建 translation_service 失败 {str(e)}：{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"构建 translation_service 失败 {str(e)}")


@router.post("/translate",
             dependencies=[Depends(verify_api_token)],
             response_model=TranslationResponse,
             summary="文本翻译",
             description="将文本从源语言翻译到中文"
             )
async def translate(
        translation_request: TranslationRequest,
        http_request: Request,
        translation_service: TranslationService = Depends(get_translation_service),
):
    logger = http_request.state.logger
    try:
        validate_translation_request(translation_request, logger)
    except HTTPException as e:
        logger.error(f"校验翻译请求出错 {str(e)}: {traceback.format_exc()}")
        raise e

    try:
        # 获得翻译结果
        trans_result = await translation_service.translate(translation_request, http_request)
        return Response(content=trans_result, media_type="application/json")
    except Exception as e:
        logger.error(f"translation 内部报错 {str(e)}: {traceback.format_exc()}")
        raise HTTPException(
            status_code=500,
            detail=f'translation 内部报错(translationRequestId={translation_request.request_id}): {str(e)}'
        )
