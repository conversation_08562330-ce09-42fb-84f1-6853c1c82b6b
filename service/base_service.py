from prometheus_client import Counter, Histogram

from core.schema.chat_request import Chat<PERSON><PERSON><PERSON>
from util.common_util import get_cur_millis


class BaseService:
    def __init__(self, counter: Counter, timer: Histogram):
        self._timer = timer
        self._counter = counter
        self._global_time_counter = {}
        self._global_token_counter = {}

    def log_elapse(self, label, chat_request: ChatRequest):
        elapsed_time = (get_cur_millis() - chat_request.request_receive_time) / 1000
        chat_request.logger.debug(f"{label}开始前耗时：{elapsed_time:.2f} 秒")
