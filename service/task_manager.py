import asyncio

from core.schema.chat_request import ChatRe<PERSON>
from core.schema.task import Task


class TaskManager:

    @classmethod
    def create_task(cls, coro, *, name=None, key=0):
        loop = asyncio.get_running_loop()
        return Task(coro, loop=loop, name=name, key=key)

    @classmethod
    async def cancel_task_dict(cls, chat_request: ChatRequest):
        if chat_request.task_dict is None:
            return
        for task_name in chat_request.task_dict:
            task = chat_request.task_dict[task_name]
            if task is None:
                continue

            if task.done():
                chat_request.logger.debug(f"{task_name} 无需取消或已结束")
                continue

            task.cancel()
            try:
                await task
            except (asyncio.CancelledError, StopAsyncIteration):
                chat_request.logger.debug(f"{task_name} 已取消")
            except Exception as e:
                chat_request.logger.error(f"{task_name} 取消时出错: {e}")
