import json
from loguru import logger
from fastapi import Request
from core.schema.translation_base import TranslationRequest, TranslationResponse
from service.model_manager import ModelManager


class TranslationService:
    def __init__(
            self,
            logger: logger,
            request_id: str,
            api_key: str,
            model_manager: ModelManager,
    ):
        self.logger = logger
        self.request_id = request_id
        self.api_key = api_key
        self.model_manager = model_manager

    async def translate(self, translation_request: TranslationRequest, http_request: Request):
        if await http_request.is_disconnected():
            self.logger.error(f"客户端已断开连接, 请求id为||{translation_request.request_id}||")
            raise RuntimeError("客户端已断开连接")

        content = translation_request.content
        translated_text = await self.model_manager.translate_any2zn(content, self.api_key,
                                                                    translation_request.request_id)
        translated_content = TranslationResponse(
            request_id=translation_request.request_id, content=translated_text,
        ).to_dict()
        return f"{json.dumps(translated_content)}\n\n"
