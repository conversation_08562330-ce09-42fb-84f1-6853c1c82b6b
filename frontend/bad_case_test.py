import asyncio
import getpass
import socket
import uuid
from datetime import datetime

import aiohttp
import pandas as pd
import streamlit as st

from config.run_config import RUN_CONFIG_DICT, DOMAIN, API_ACCESS_TOKEN
from core.schema.chat_request import get_simple_chat_request_v1
from core.schema.chat_response import ChatResponse
from core.enum.event_type import EventType

from data_loader import load_item_name_list
from util.common_util import is_empty, decode_sse
from util.file_util import join_path
from util.cache_util import CacheManager, BadCaseTestConfig, get_default_bad_case_config
from util.llm_util import translate_to_chinese


def bad_case_test(project_dir):
    # 初始化缓存管理器
    cache_manager = CacheManager(project_dir)

    # 加载缓存配置
    cached_config = cache_manager.load_config("bad_case_test", BadCaseTestConfig)
    if cached_config is None:
        cached_config = get_default_bad_case_config()

    # 设置页面布局
    sidebar = st.sidebar
    header = st.container()
    input_area = st.container()
    results_area = st.container()

    # 在侧边栏显示选择框
    with sidebar:
        path = join_path(project_dir, "config", "item_id_name.json")
        candidates, _ = load_item_name_list(path)
        candidates.append("UNK")

        ENV_NAME_DICT = {"本地": "local", "测试": "test", "预发": "preview", "生产": "prod"}
        ENV_DISPLAY_DICT = {"local": "本地", "test": "测试", "preview": "预发", "prod": "生产"}

        # 使用缓存的默认值
        default_item_index = 0
        if cached_config.selected_item in candidates:
            default_item_index = candidates.index(cached_config.selected_item)

        default_env_index = 0
        env_options = ["预发", "本地", "测试"]
        cached_env_display = ENV_DISPLAY_DICT.get(cached_config.env, "预发")
        if cached_env_display in env_options:
            default_env_index = env_options.index(cached_env_display)

        selected_item = st.selectbox("选择机型", candidates, key='product', index=default_item_index)
        env_display = st.selectbox("选择环境", env_options, key='env', index=default_env_index)
        env = ENV_NAME_DICT[env_display]

        # 配置并发请求数量，使用缓存的默认值
        concurrent_requests = st.number_input("并发请求数量", min_value=1, max_value=50,
                                              value=cached_config.concurrent_requests)

    # 检查配置是否有变化，如果有则自动保存
    current_question = st.session_state.get("user_question", "")
    if (selected_item != cached_config.selected_item or
            env != cached_config.env or
            concurrent_requests != cached_config.concurrent_requests or
            (current_question and current_question != cached_config.user_question)):
        # 自动保存配置
        auto_save_config = BadCaseTestConfig(
            selected_item=selected_item,
            env=env,
            user_question=current_question,
            concurrent_requests=concurrent_requests,
            last_updated=datetime.now().isoformat()
        )
        cache_manager.save_config("bad_case_test", auto_save_config)

    # 在页面顶部显示标题
    with header:
        st.title("缺陷验证（bad case）")

    # 在输入区域显示问题输入框和提交按钮
    with input_area:
        # 初始化缓存的问题到 session_state
        if "user_question" not in st.session_state:
            st.session_state.user_question = cached_config.user_question

        user_question = st.text_input("请输入问题", value=st.session_state.user_question)
        # 更新 session_state
        st.session_state.user_question = user_question

        submit_button = st.button("提交并发请求", type="primary")

    # 初始化 session_state 存储请求结果
    if "bad_case_results" not in st.session_state:
        st.session_state.bad_case_results = []

    # 处理提交请求
    if submit_button and user_question:
        # 自动保存当前配置
        current_config = BadCaseTestConfig(
            selected_item=selected_item,
            env=env,
            user_question=user_question,
            concurrent_requests=concurrent_requests,
            last_updated=datetime.now().isoformat()
        )
        cache_manager.save_config("bad_case_test", current_config)

        with st.spinner(f"正在发送 {concurrent_requests} 个并发请求...请稍候"):
            # 清空之前的结果
            st.session_state.bad_case_results = []

            # 生成请求 ID
            request_id_base = f"{getpass.getuser()}@{socket.gethostname()}-{uuid.uuid4()}"

            # 执行并发请求
            results = asyncio.run(run_concurrent_requests(
                query=user_question,
                product_id=selected_item,
                env=env,
                request_id_base=request_id_base,
                concurrent_count=concurrent_requests
            ))

            # 存储结果
            st.session_state.bad_case_results = results

            # 显示成功消息
            st.success(f"已完成 {len(results)} 个请求")

    # 显示结果
    with results_area:
        if st.session_state.bad_case_results:
            # 创建结果表格
            results_df = pd.DataFrame(st.session_state.bad_case_results)

            # 显示统计信息
            st.subheader("请求统计")
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                avg_first_token = results_df["first_token_time"].mean()
                st.metric("平均首Token时间", f"{avg_first_token:.2f}秒")

            with col2:
                avg_time = results_df["total_time"].mean()
                st.metric("平均回复耗时（前端）", f"{avg_time:.2f}秒")

            with col3:
                avg_time_ai = results_df["total_time_ai"].mean()
                st.metric("平均回复耗时（算法）", f"{avg_time_ai:.2f}秒")

            with col4:
                avg_tokens = results_df["total_tokens"].mean()
                st.metric("平均Token消耗", f"{avg_tokens:.1f}")

            # 显示详细结果
            st.subheader("详细结果")
            st.dataframe(results_df, hide_index=True)

            # 提供下载按钮
            timestamp = datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
            csv = results_df.to_csv(index=False)
            st.download_button(
                label="下载结果为CSV",
                data=csv,
                file_name=f"bad_case_results_{timestamp}.csv",
                mime="text/csv",
            )


async def run_concurrent_requests(query, product_id, env, request_id_base, concurrent_count):
    """执行多个并发请求并返回结果列表"""
    # 初始化翻译缓存
    if "translation_cache" not in st.session_state:
        st.session_state.translation_cache = {}

    query_zh = translate_to_chinese(query)
    async with aiohttp.ClientSession() as session:
        tasks = []
        for i in range(concurrent_count):
            # 为每个请求生成唯一的请求ID
            request_id = f"{request_id_base}-{i + 1}"
            task = asyncio.create_task(
                make_single_request(session, query, query_zh, product_id, env, request_id, i + 1))
            tasks.append(task)

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)
        return results


async def make_single_request(session, query, query_zh, product_id, env, request_id, request_num):
    """发送单个请求并处理响应"""
    url = f"http://{RUN_CONFIG_DICT[env][DOMAIN]}/api/v1/chat"
    headers = {
        "Connection": "keep-alive",
        "Content-Type": "application/json",
        "Authorization": RUN_CONFIG_DICT[env][API_ACCESS_TOKEN],
        "X-Request-ID": "streamlit",
    }
    # 创建 ChatRequest 对象
    # ToDo(hm): 这里应该支持多版本
    chat_request = get_simple_chat_request_v1(query, product_id, request_id)
    # 转换为字典用于API请求
    data = chat_request.to_dict()
    result = {
        "response": "",
        "response_zh": "",
        "query": query,
        "query_zh": query_zh,
        "request_num": request_num,
        "request_id": request_id,
        "product_id": product_id,
        "model_version": "",
        "first_token_time": 0,
        "total_time": 0,
        "total_tokens": 0,
        "answer_type": "",
    }

    start_time = asyncio.get_event_loop().time()
    try:
        async with session.post(url, headers=headers, json=data) as response:
            if response.status != 200:
                result["response"] = f"请求失败，状态码: {response.status}"
                result["total_time"] = asyncio.get_event_loop().time() - start_time
                return result

            async for line in response.content:
                chunk = line.decode("utf-8").strip()
                if is_empty(chunk):
                    continue

                response_dict = decode_sse(chunk)
                if is_empty(response_dict) or "event" not in response_dict:
                    continue

                response = ChatResponse.from_dict(response_dict)
                cur_event_type = response_dict["event"]
                if cur_event_type == EventType.TEXT_CHUNK_EVENT.value:
                    result["response"] += response_dict["data"]["text"]
                if cur_event_type == EventType.FINISH_EVENT.value:
                    result["request_id"] = response_dict["request_id"]
                    result["model_version"] = response_dict["data"]["model_version"]
                    result["first_token_time"] = (response_dict["data"]["answer_start_time"] -
                                                  response_dict["data"]["request_receive_time"]) / 1000
                    result["total_time"] = asyncio.get_event_loop().time() - start_time
                    result["total_time_ai"] = (response_dict["data"]["answer_finish_time"] -
                                               response_dict["data"]["answer_start_time"]) / 1000
                    result["total_tokens"] = response_dict["data"]["total_tokens"]
                    result["answer_type"] = response.data.answer_type.description
    except Exception as e:
        result["response"] = f"请求异常: {str(e)}"
        result["total_time"] = asyncio.get_event_loop().time() - start_time

    # 使用缓存进行翻译
    response_text = result["response"]
    if response_text in st.session_state.translation_cache:
        # 如果已经翻译过，直接使用缓存的结果
        result["response_zh"] = st.session_state.translation_cache[response_text]
    else:
        # 如果没有翻译过，进行翻译并缓存
        result["response_zh"] = translate_to_chinese(response_text)
        st.session_state.translation_cache[response_text] = result["response_zh"]

    return result
