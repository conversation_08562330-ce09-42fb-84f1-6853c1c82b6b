import os
import pandas as pd
import streamlit as st
from pathlib import Path
from datetime import datetime

from util.file_util import join_path, mkdir_if_not_exists


def get_excel_files(directory):
    """Get all Excel files in the specified directory"""
    excel_files = list(Path(directory).glob("*.xlsx"))
    # Sort by filename in descending order (assuming filenames contain dates)
    excel_files.sort(reverse=True)
    return excel_files


def get_latest_excel_file(directory):
    """Find the latest Excel file in the specified directory"""
    excel_files = get_excel_files(directory)
    if not excel_files:
        return None
    return excel_files[0]


def save_dataframe_to_excel(df, file_path):
    """Save the DataFrame to an Excel file"""
    df.to_excel(file_path, index=False)


def label(project_dir):
    """Main function for the labeling page"""
    # 创建主要布局
    sidebar = st.sidebar
    main_content = st.container()

    # 确保 chat_message 目录存在
    chat_message_dir = join_path(project_dir, "data", "chat_message")
    mkdir_if_not_exists(chat_message_dir)

    # 在侧边栏显示环境选择框
    with sidebar:
        st.title("标注工具")
        st.info("此页面用于标注聊天数据")

        # 获取所有 Excel 文件
        excel_files = get_excel_files(chat_message_dir)
        file_options = [str(file) for file in excel_files]
        file_names = [os.path.basename(file) for file in file_options]

        # 创建文件选择下拉框
        if file_options:
            # 如果 session_state 中没有 selected_file_index，则默认选择最新的文件（索引0）
            if "selected_file_index" not in st.session_state:
                st.session_state["selected_file_index"] = 0

            selected_index = st.selectbox(
                "选择文件",
                range(len(file_names)),
                format_func=lambda i: file_names[i],
                index=st.session_state["selected_file_index"]
            )

            # 更新 session_state 中的 selected_file_index 和 current_file
            st.session_state["selected_file_index"] = selected_index
            st.session_state["current_file"] = file_options[selected_index]

        # 上传新的 Excel 文件
        uploaded_file = st.file_uploader("上传新的 Excel 文件", type=["xlsx"])
        if uploaded_file:
            # 生成带时间戳的文件名
            timestamp = datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
            file_name = f"{timestamp}-{uploaded_file.name}"
            file_path = join_path(chat_message_dir, file_name)

            # 保存上传的文件
            with open(file_path, "wb") as f:
                f.write(uploaded_file.getbuffer())

            st.success(f"文件已上传: {file_name}")
            st.session_state["current_file"] = file_path

            # 刷新页面以更新文件列表
            st.rerun()

    with main_content:
        st.title("聊天数据标注")

        # 如果 session_state 中有当前文件，使用它；否则使用最新的文件
        latest_file = get_latest_excel_file(chat_message_dir)
        current_file = st.session_state.get("current_file", latest_file)

        if current_file is None:
            st.warning("未找到 Excel 文件，请上传一个文件")
            return

        # 显示当前使用的文件
        st.info(f"当前使用的文件: {os.path.basename(current_file)}")

        try:
            # 读取 Excel 文件
            df = pd.read_excel(current_file)

            # 创建一个可编辑的数据表格
            edited_df = st.data_editor(
                df,
                use_container_width=True,
                num_rows="dynamic",
                key="data_editor"
            )

            # 手动保存按钮
            if st.button("保存修改", type="primary"):
                save_dataframe_to_excel(edited_df, current_file)
                st.success("数据已成功保存！")

        except Exception as e:
            st.error(f"读取或处理文件时出错: {str(e)}")
