import getpass
import multiprocessing
import os
import re
import socket
import subprocess
from datetime import datetime

import streamlit as st

from util.common_util import is_empty, not_empty
from util.process_util import get_child_process_count, get_cur_process_start_time, kill_process_and_children


def ops(project_dir):
    with st.container(border=True):
        ops_action(project_dir, branch_name="test")


def git_log_in_directory(project_dir):
    process = subprocess.Popen(['git', 'log', '-1'], cwd=project_dir, stdout=subprocess.PIPE)
    output, error = process.communicate()
    if process.returncode != 0:
        # Handle error
        print(f"Error: {error}")

    return output.decode('utf-8')


def get_code_version(project_dir):
    try:
        git_log = git_log_in_directory(project_dir)
        # git_log = subprocess.check_output(['git', 'log', '-1']).decode('utf-8')
        date_regex = r'Date:\s+(.+)'
        message_regex = r'\n\n\s+(.+)'
        date_match = re.search(date_regex, git_log)
        message_match = re.search(message_regex, git_log)
        if date_match and message_match:
            date_str = date_match.group(1)
            message = message_match.group(1)
            date_object = datetime.strptime(date_str, "%a %b %d %H:%M:%S %Y %z")
            formatted_date_string = date_object.strftime("%Y-%m-%d %H:%M:%S")
            return f"{message}({formatted_date_string})"
        return f'Cannot get git log'
    except subprocess.CalledProcessError as e:
        return f'Cannot get git log: {str(e)}'


def git_pull(project_dir, branch, tag=None):
    # 改变当前工作目录
    os.chdir(project_dir)
    # 使用 subprocess 来执行 git 命令
    subprocess.check_call(["git", "checkout", "main"])
    subprocess.check_call(["git", "pull"])
    try:
        subprocess.check_call(["git", "branch", "-D", branch])
    except subprocess.CalledProcessError as e:
        print(f"Error: {str(e)}")
    subprocess.check_call(["git", "checkout", branch])
    subprocess.check_call(["git", "pull", "origin", branch])
    if not_empty(tag):
        subprocess.check_call(["git", "checkout", tag])


def ops_action(project_dir, branch_name):
    info_dict = {
        "代码版本": get_code_version(project_dir),
        "运行环境": f"{getpass.getuser()}@{socket.gethostname()}",
        "cpu 数量": multiprocessing.cpu_count(),
        "当前子进程数": get_child_process_count(os.getpid()),
        "系统启动时间": get_cur_process_start_time()
    }
    st.json(info_dict)
    col1, col2 = st.columns([1, 1])
    with col1:
        branch_name_in = st.text_input("分支", value=branch_name, key="branch_name_in")
    with col2:
        tag_in = st.text_input("tag", value="", key="tag_in")
    col1, col2, col3 = st.columns([1, 1, 1])
    with col1:
        if is_empty(branch_name_in):
            st.warning("输入正确的分支信息")
        else:
            version_info = f"{branch_name_in} 分支" if is_empty(tag_in) else f"{branch_name_in} 分支，tag:{tag_in}"
            if st.button(f"拉取最新代码（{version_info}）"):
                # ToDo(hm): pip install -r when pull in ops?
                git_pull(project_dir, branch_name_in, tag_in)
                st.session_state.clear()
    with col2:
        st.button("刷新页面")
    with col3:
        if st.button("重启系统", type="primary"):
            kill_process_and_children(os.getpid())
