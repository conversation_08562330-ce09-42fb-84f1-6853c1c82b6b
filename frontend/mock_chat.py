import getpass
import json
import socket
import uuid

import aiohttp
import streamlit as st

from config.run_config import RUN_CONFIG_DICT, DOMAIN, API_ACCESS_TOKEN
from core.enum.message_type import MessageType
from core.schema.chat_request import ChatRequest, Message
from core.schema.chat_round import ChatRound
from core.enum.role import Role
from core.enum.response_mode import ResponseMode
from core.enum.language import Language
from core.enum.area import Area
from core.enum.event_type import EventType
from core.schema.constant import ENV_SHOW_NAME_DICT

from data_loader import load_item_name_list
from util.common_util import is_empty, decode_sse
from util.file_util import join_path
from util.llm_util import translate


def mock_chat(project_dir):
    # 创建主要布局
    sidebar = st.sidebar
    header = st.container()
    chat_area = st.container()
    question_area = st.container()

    # 在侧边栏显示选择框
    with sidebar:
        path = join_path(project_dir, "config", "item_id_name.json")
        candidates, _ = load_item_name_list(path)
        candidates.append("UNK")

        language = st.selectbox("选择语言", ["印尼语", "中文"], key='language')
        selected_item = st.selectbox("选择机型", candidates, key='product')
        env = st.selectbox("选择环境", ["预发", "本地", "测试", "生产"], key='env')
        env = ENV_SHOW_NAME_DICT[env]
        debug_by_chinese = language == "中文"

        # 添加一个分隔线
        st.markdown("---")

        # 添加下载最近的chat_request按钮
        if "last_chat_request" in st.session_state and st.session_state.last_chat_request is not None:
            # 将chat_request转换为JSON字符串，并格式化以便于阅读
            chat_request_json = json.dumps(st.session_state.last_chat_request.to_dict(), indent=2, ensure_ascii=False)

            # 添加下载按钮
            st.download_button(
                label="下载最近的chat_request",
                data=chat_request_json,
                file_name="chat_request.json",
                mime="application/json"
            )

            # 添加复制按钮
            st.code(chat_request_json, language="json")

    # 在页面顶部显示标题
    with header:
        st.title("国际促销员 Copilot (430版本)")

    # Initialize chat history
    if "messages" not in st.session_state:
        st.session_state.messages = []

    # Initialize selected_question in session state if it doesn't exist
    if "selected_question" not in st.session_state:
        st.session_state.selected_question = None

    # Initialize last_chat_request in session state if it doesn't exist
    if "last_chat_request" not in st.session_state:
        st.session_state.last_chat_request = None

    # 在聊天区域显示聊天历史
    with chat_area:
        # Display chat messages from history on app rerun
        for message in st.session_state.messages:
            with st.chat_message(message["role"]):
                st.markdown(message["content"], unsafe_allow_html=True)

    # 在页面底部显示预设问题按钮和输入框
    # 注意：st.chat_input 必须在最外层调用，不能在 with 块中调用
    user_input = st.chat_input("What is up?", key='prompt')

    # 在输入框上方显示预设问题按钮
    with question_area:
        st.write("常见问题：")
        col1, col2, col3 = st.columns(3)

        # Define the built-in questions
        question1 = "屏幕多大？"
        question2 = "手机重量是多少？"
        question3 = "电池续航多少？"

        # Create buttons for each question
        if col1.button(question1):
            st.session_state.selected_question = question1
            st.rerun()

        if col2.button(question2):
            st.session_state.selected_question = question2
            st.rerun()

        if col3.button(question3):
            st.session_state.selected_question = question3
            st.rerun()

    # Process input from buttons
    if st.session_state.selected_question is not None:
        button_prompt = st.session_state.selected_question
        # Reset selected_question to avoid processing it again
        st.session_state.selected_question = None

        # Add user message to chat history
        st.session_state.messages.append({"role": "user", "content": button_prompt})
        translated_prompt = button_prompt
        if debug_by_chinese:
            translated_prompt = translate(button_prompt)
            st.session_state.messages.append({"role": "user", "content": translated_prompt})

        # Generate a request ID
        request_id = f"{getpass.getuser()}@{socket.gethostname()}-{uuid.uuid4()}"

        # Display user message in chat message container
        with chat_area:
            with st.chat_message("user"):
                st.markdown(f'{button_prompt}(chatRequestId: "{request_id}")')
                if debug_by_chinese:
                    st.markdown(f'{translated_prompt}(chatRequestId: "{request_id}")')

            # Display assistant response in chat message container
            with st.chat_message("assistant"):
                if debug_by_chinese:
                    response = st.write_stream(
                        response_generator(translated_prompt, selected_item, env, request_id)
                    )
                    st.session_state.messages.append({"role": "assistant", "content": response})
                    translated_response = translate(response, from_lang="印尼语", to_lang="中文")
                    st.session_state.messages.append(
                        {
                            "role": "assistant",
                            "content": translated_response,
                        }
                    )
                    st.markdown(translated_response, unsafe_allow_html=True)
                else:
                    response = st.write_stream(response_generator(button_prompt, selected_item, env, request_id))
                    st.session_state.messages.append({"role": "assistant", "content": response})

    # Process input from chat input
    if user_input:
        # Add user message to chat history
        st.session_state.messages.append({"role": "user", "content": user_input})
        translated_prompt = user_input
        if debug_by_chinese:
            translated_prompt = translate(user_input)
            st.session_state.messages.append({"role": "user", "content": translated_prompt})

        # Generate a request ID
        request_id = f"{getpass.getuser()}@{socket.gethostname()}-{uuid.uuid4()}"

        # Display user message in chat message container
        with chat_area:
            with st.chat_message("user"):
                st.markdown(f'{user_input}(chatRequestId: "{request_id}")')
                if debug_by_chinese:
                    st.markdown(f'{translated_prompt}(chatRequestId: "{request_id}")')

            # Display assistant response in chat message container
            with st.chat_message("assistant"):
                if debug_by_chinese:
                    response = st.write_stream(
                        response_generator(translated_prompt, selected_item, env, request_id)
                    )
                    st.session_state.messages.append({"role": "assistant", "content": response})
                    translated_response = translate(response, from_lang="印尼语", to_lang="中文")
                    st.session_state.messages.append(
                        {
                            "role": "assistant",
                            "content": translated_response,
                        }
                    )
                    st.markdown(translated_response, unsafe_allow_html=True)
                else:
                    response = st.write_stream(response_generator(user_input, selected_item, env, request_id))
                    st.session_state.messages.append({"role": "assistant", "content": response})


async def response_generator(query, product_id, env, request_id):
    url = f"http://{RUN_CONFIG_DICT[env][DOMAIN]}/api/v1/chat"
    headers = {
        "Connection": "keep-alive",
        "Content-Type": "application/json",
        "Authorization": RUN_CONFIG_DICT[env][API_ACCESS_TOKEN],
        "X-Request-ID": "streamlit",
    }

    # Create a ChatRequest object
    chat_request = ChatRequest(
        area=Area.INDONESIA,
        site=0,
        category_name="0",
        category_id="0",
        user_id="0",
        item_id="0",
        org_id="0",
        conversation_id="0",
        language=Language.ENGLISH,
        item_name=product_id,
        chat_history=[
            ChatRound(
                role=Role.USER,
                messages=[
                    Message(
                        content=query,
                        type=MessageType.TEXT
                    )
                ]
            )
        ],
        response_mode=ResponseMode.STREAMING,
        request_id=request_id,
        debug=True,
    )

    # 保存chat_request到session state
    st.session_state.last_chat_request = chat_request

    # Convert the ChatRequest to a dictionary for the API request
    data = chat_request.to_dict()

    async with aiohttp.ClientSession() as session:
        async with session.post(url, headers=headers, json=data) as response:
            if response.status != 200:
                yield f"请求失败，状态码: {response.status},响应内容:{await response.text()}"
                return

            async for line in response.content:
                # 去除多余的新行和空行
                chunk = line.decode("utf-8").strip()
                if is_empty(chunk):
                    continue

                response_dict = decode_sse(chunk)
                if is_empty(response_dict) or "event" not in response_dict:
                    continue

                cur_event_type = response_dict["event"]
                # if cur_event_type == EventType.START_EVENT.value:
                #     yield "Answer: "

                if cur_event_type == EventType.TEXT_CHUNK_EVENT.value:
                    yield response_dict["data"]["text"]

                if cur_event_type == EventType.FINISH_EVENT.value:
                    request_id = response_dict["request_id"]
                    first_token_elapse = (
                                                 response_dict["data"]["answer_start_time"]
                                                 - response_dict["data"]["request_receive_time"]
                                         ) / 1000

                    answer_elapse = (
                                            response_dict["data"]["answer_finish_time"]
                                            - response_dict["data"]["answer_start_time"]
                                    ) / 1000
                    model_version = response_dict["data"]["model_version"]
                    cost_total_tokens = response_dict["data"]["total_tokens"]
                    msg = f'(开始回答耗时={first_token_elapse:.2f}秒, 回答耗时={answer_elapse}s, model_version={model_version}, chatRequestId: "{request_id}", 回答消耗总tokens={cost_total_tokens})'
                    yield msg
                    return
