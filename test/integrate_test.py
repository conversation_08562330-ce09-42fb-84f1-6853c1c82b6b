import json
import unittest

from core.schema.chat_request import ChatRequest
from frontend.base_unit import call_chat
from util.common_util import pprint


class TestHistoryChat(unittest.TestCase):
    def test_chat(self):
        with open("test_data/1_双机对比_输入.json", "r") as f:
            data = json.load(f)
        chat_request = ChatRequest.from_dict(data)
        response = call_chat(chat_request)
        pprint(response)




if __name__ == '__main__':
    unittest.main()
