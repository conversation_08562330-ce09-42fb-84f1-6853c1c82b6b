import unittest
from core.enum.message_type import MessageType
from core.schema.constant import UNDEFINED
from util.common_util import assert_not_eq


class TestMessageType(unittest.TestCase):
    def test_message_type_values(self):
        """Test that the integer values of MessageType are preserved"""
        self.assertEqual(int(MessageType.UNKNOWN), 0)
        self.assertEqual(int(MessageType.TEXT), 1)
        self.assertEqual(int(MessageType.NOT_IN_SCOPE), 2)
        self.assertEqual(int(MessageType.ITEM_CANDIDATE), 9)
        self.assertEqual(int(MessageType.FREE_FAQ_ANSWER), 14)

    def test_message_type_description(self):
        """Test that the descriptions are correctly associated with each enum value"""
        self.assertEqual(MessageType.UNKNOWN.description, "未知")
        self.assertEqual(MessageType.TEXT.description, "正常文本信息")
        self.assertEqual(MessageType.NOT_IN_SCOPE.description, "非手机问题拒答")
        self.assertEqual(MessageType.ITEM_CANDIDATE.description, "给出候选项")
        self.assertEqual(MessageType.FREE_FAQ_ANSWER.description, "自由问答中可以回答的部分")

    def test_valid_description(self):
        for message_type in MessageType:
            assert_not_eq(UNDEFINED, message_type.description)


if __name__ == "__main__":
    unittest.main()
