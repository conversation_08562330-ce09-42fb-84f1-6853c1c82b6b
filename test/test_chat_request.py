import unittest
from core.schema.chat_request import ChatRequest
from core.schema.chat_round import ChatRound
from core.enum.role import Role
from core.schema.message import Message
from core.schema.item import Item
from core.enum.message_type import MessageType


class TestChatRequest(unittest.TestCase):
    def test_from_dict(self):
        # Create a test dictionary representing a ChatRequest
        test_dict = {
            "area": 1,
            "language": 3,
            "conversation_id": "test-conv-123",
            "request_id": "test-req-456",
            "chat_history": [
                {
                    "role": 1,  # USER
                    "messages": [
                        {
                            "type": 1,  # TEXT
                            "content": "Hello, I have a question about Redmi Note 12"
                        }
                    ]
                },
                {
                    "role": 2,  # ASSISTANT
                    "messages": [
                        {
                            "type": 9,  # ITEM_CANDIDATE
                            "content": "Please select your device:",
                            "item_list": [
                                {
                                    "item_id": "redmi-note-12-4g",
                                    "item_name": "Redmi Note 12 4G",
                                    "category_id": "smartphone",
                                    "category_name": "Smartphone"
                                },
                                {
                                    "item_id": "redmi-note-12-5g",
                                    "item_name": "Redmi Note 12 5G",
                                    "category_id": "smartphone",
                                    "category_name": "Smartphone"
                                }
                            ]
                        }
                    ]
                },
                {
                    "role": 1,  # USER
                    "messages": [
                        {
                            "type": 1,  # TEXT
                            "content": "I choose Redmi Note 12 4G",
                            "selected_item": {
                                "item_id": "redmi-note-12-4g",
                                "item_name": "Redmi Note 12 4G",
                                "category_id": "smartphone",
                                "category_name": "Smartphone"
                            }
                        }
                    ]
                }
            ]
        }
        
        # Convert the dictionary to a ChatRequest object
        chat_request = ChatRequest.from_dict(test_dict)
        
        # Verify the conversion was successful
        self.assertEqual(chat_request.area, 1)
        self.assertEqual(chat_request.language, 3)
        self.assertEqual(chat_request.conversation_id, "test-conv-123")
        self.assertEqual(chat_request.request_id, "test-req-456")
        
        # Verify chat_history was properly converted
        self.assertEqual(len(chat_request.chat_history), 3)
        
        # Check first round (USER)
        first_round = chat_request.chat_history[0]
        self.assertEqual(first_round.role, Role.USER)
        self.assertEqual(len(first_round.messages), 1)
        self.assertEqual(first_round.messages[0].content, "Hello, I have a question about Redmi Note 12")
        self.assertEqual(first_round.messages[0].type, MessageType.TEXT)
        
        # Check second round (ASSISTANT with item_list)
        second_round = chat_request.chat_history[1]
        self.assertEqual(second_round.role, Role.ASSISTANT)
        self.assertEqual(len(second_round.messages), 1)
        self.assertEqual(second_round.messages[0].content, "Please select your device:")
        self.assertEqual(second_round.messages[0].type, MessageType.ITEM_CANDIDATE)
        self.assertEqual(len(second_round.messages[0].item_list), 2)
        self.assertEqual(second_round.messages[0].item_list[0].item_name, "Redmi Note 12 4G")
        self.assertEqual(second_round.messages[0].item_list[1].item_name, "Redmi Note 12 5G")
        
        # Check third round (USER with selected_item)
        third_round = chat_request.chat_history[2]
        self.assertEqual(third_round.role, Role.USER)
        self.assertEqual(len(third_round.messages), 1)
        self.assertEqual(third_round.messages[0].content, "I choose Redmi Note 12 4G")
        self.assertEqual(third_round.messages[0].type, MessageType.TEXT)
        self.assertIsNotNone(third_round.messages[0].selected_item)
        self.assertEqual(third_round.messages[0].selected_item.item_name, "Redmi Note 12 4G")
        self.assertEqual(third_round.messages[0].selected_item.item_id, "redmi-note-12-4g")
        
        # Test round-trip conversion (to_dict and back)
        dict_data = chat_request.to_dict()
        new_chat_request = ChatRequest.from_dict(dict_data)
        self.assertEqual(len(new_chat_request.chat_history), 3)
        self.assertEqual(new_chat_request.chat_history[2].messages[0].selected_item.item_name, "Redmi Note 12 4G")


if __name__ == "__main__":
    unittest.main()
