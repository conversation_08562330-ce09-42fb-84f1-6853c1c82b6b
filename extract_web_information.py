import requests
from bs4 import BeautifulSoup
import re
from collections import defaultdict


def extract_product_description(url):
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    }

    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.encoding = response.apparent_encoding
        if response.status_code != 200:
            return f"请求失败，状态码：{response.status_code}"

        soup = BeautifulSoup(response.text, 'html.parser')

        # 移除无关元素
        for tag in soup(['script', 'style', 'header', 'footer', 'nav', 'aside', 'form']):
            tag.decompose()

        # 识别产品标题所在区域
        product_title = None
        title_tags = soup.find_all(['h1', 'h2', 'h3'])
        for tag in title_tags:
            text = tag.get_text(strip=True)
            if "小米" in text or "Xiaomi" in text or "红米" in text:
                product_title = text
                break

        if not product_title:
            return "未找到产品标题"

        # 基于标题定位产品描述区域
        description_region = None
        current_tag = soup.find(string=lambda text: product_title in text if text else False)

        if current_tag:
            # 向上查找父容器
            parent = current_tag.parent
            while parent and len(parent.get_text(strip=True)) < 200:
                parent = parent.parent

            if parent:
                description_region = parent
            else:
                # 如果找不到合适的父容器，尝试向下查找兄弟节点
                siblings = []
                for sibling in current_tag.parent.find_next_siblings():
                    if sibling.name in ['p', 'div', 'ul']:
                        siblings.append(sibling)
                        if len(' '.join([s.get_text(strip=True) for s in siblings])) > 300:
                            break
                if siblings:
                    description_region = BeautifulSoup('<div></div>', 'html.parser')
                    for s in siblings:
                        description_region.append(s)

        if not description_region:
            #return "未找到产品标题"
            # 作为兜底，提取页面中最长的文本块
            text_blocks = []
            for tag in soup.find_all(['p', 'div']):
                text = tag.get_text(strip=True)
                if len(text) > 100:
                    text_blocks.append((len(text), tag))

            if text_blocks:
                text_blocks.sort(key=lambda x: x[0], reverse=True)
                description_region = text_blocks[0][1]

        # 提取并清理文本
        if description_region:
            full_text = description_region.get_text(separator='\n', strip=True)
            # 清理多余空行和空格
            full_text = re.sub(r'\n{2,}', '\n', full_text)
            full_text = re.sub(r' +', ' ', full_text)

            # 识别产品描述段落（通常是连续的长文本）
            paragraphs = full_text.split('\n')
            product_desc = []
            current_paragraph = ""

            for p in paragraphs:

                if len(p) > 50:  # 只保留较长的段落
                    print(f"len(p) - {len(p)} - p - {p} ")
                    if current_paragraph:
                        current_paragraph += " " + p
                    else:
                        current_paragraph = p
                elif current_paragraph:
                    product_desc.append(current_paragraph)
                    current_paragraph = ""

            if current_paragraph:
                product_desc.append(current_paragraph)

            # 合并成完整描述
            if len(product_desc[0]) > 100: # 进行获取信息的更改
                return product_desc[0]  # 返回最长的产品描述段落

        return "未能提取到产品描述"

    except Exception as e:
        return f"提取失败：{str(e)}"


url = "https://detail.zol.com.cn/cell_phone/index1441059.shtml"
print(extract_product_description(url))