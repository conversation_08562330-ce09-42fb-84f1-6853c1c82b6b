import re
import json
import requests
import os
from config.model_config import MODEL_VERSION
from core.schema.constant import TEST_ENV, PREVIEW_ENV, PROD_ENV

ENVIRONMENT = PREVIEW_ENV


def update_knowledge_base():
    space_dict = {
        TEST_ENV: "dataset-TsPXNbDYMXJw4a8iQng4ACch",
        PREVIEW_ENV: "dataset-jw0XHS9hJ8w6AoSVquSWlunR",
        PROD_ENV: "dataset-G2EyyAhamqJZJwViVooyKY8v"
    }
    headers = {
        "Authorization": "Bearer " + space_dict[ENVIRONMENT]
    }

    """
    kb_list = requests.get("https://mify-be.pt.xiaomi.com/api/v1/datasets?page=1&limit=500", headers=headers).json()["data"]
    for kb in kb_list:
        #删除知识库
        kb_id = kb["id"]
        if "v0.0.8" in kb["name"]:
            res = requests.delete(f"https://mify-be.pt.xiaomi.com/api/v1/datasets/{kb_id}", headers=headers)
    """
    
    kb_name2id = {}
    src_data_path = "../datalake/knowledge_base/"
    for spu in os.listdir(src_data_path):
        if not re.compile("^\.").match(spu):
            #创建知识库
            data = {
                "name": spu + f"({MODEL_VERSION})",
                "description": spu + f"({MODEL_VERSION})",
                "indexing_technique": "high_quality",
                "permission": "all_team_members"
            }
            res = requests.post("https://mify-be.pt.xiaomi.com/api/v1/datasets", headers=headers, json=data)
            kb_id = res.json()["id"]
            kb_name2id[spu] = kb_id

            #添加文档
            data = {
                "indexing_technique": "high_quality",
                "doc_form": "text_model",
                "process_rule": {
                    "rules": {
                        "pre_processing_rules": [
                            {
                                "id":"remove_extra_spaces",
                                "enabled": True
                            },
                            {
                                "id":"remove_urls_emails",
                                "enabled": True
                            }
                        ],
                        "segmentation": {
                            "separator": "\n\n",
                            "max_tokens": 4000
                        },
                        "subchunk_segmentation": {
                            "separator": "\n\n",
                            "max_tokens": 4000,
                            "chunk_overlap": 0
                        }
                    },
                    "mode":"custom"
                },
                "retrieval_model": {
                    "search_method": "hybrid_search",
                    "reranking_enable": True,
                    "reranking_model": {
                        "reranking_provider_name": "cloudml",
                        "reranking_model_name": "bge-reranker-large"
                    },
                    "top_k": 20,
                    "score_threshold_enabled": False,
                    "score_threshold": 1.0
                },
                "embedding_model": "text-embedding-3-large",
                "embedding_model_provider": "Azure OpenAI Service Model"
            }
            if os.path.exists(src_data_path + spu + "/FAQ_2.txt"):
                files = {
                    "data": (None, json.dumps(data), "text/plain"),
                    "file": ("FAQ_2.txt", open(src_data_path + spu + "/FAQ_2.txt", "rb"))
                }
                res = requests.post(f"https://mify-be.pt.xiaomi.com/api/v1/datasets/{kb_id}/document/create-by-file", headers=headers, files=files)
            if os.path.exists(src_data_path + spu + "/EN_SALE.txt"):
                files = {
                    "data": (None, json.dumps(data), "text/plain"),
                    "file": ("EN_SALE.txt", open(src_data_path + spu + "/EN_SALE.txt", "rb"))
                }
                res = requests.post(f"https://mify-be.pt.xiaomi.com/api/v1/datasets/{kb_id}/document/create-by-file", headers=headers, files=files)
            if os.path.exists(src_data_path + spu + "/ID_SALE.txt"):
                files = {
                    "data": (None, json.dumps(data), "text/plain"),
                    "file": ("ID_SALE.txt", open(src_data_path + spu + "/ID_SALE.txt", "rb"))
                }
                res = requests.post(f"https://mify-be.pt.xiaomi.com/api/v1/datasets/{kb_id}/document/create-by-file", headers=headers, files=files)
            if os.path.exists(src_data_path + spu + "/ID_SALE.json.txt"):
                files = {
                    "data": (None, json.dumps(data), "text/plain"),
                    "file": ("ID_SALE.json.txt", open(src_data_path + spu + "/ID_SALE.json.txt", "rb"))
                }
                res = requests.post(f"https://mify-be.pt.xiaomi.com/api/v1/datasets/{kb_id}/document/create-by-file", headers=headers, files=files)

    #更新配置文件
    fout = open("./config/item_name_dataset_id_" + ENVIRONMENT + ".json", "w", encoding="utf-8")
    fout.write(json.dumps(kb_name2id, indent=2, ensure_ascii=False))
    fout.close()


if __name__ == "__main__":
    update_knowledge_base()