curl -v -N -H 'Connection:keep-alive' -H 'Content-Type:application/json' \
-H 'Authorization:Bearer 6b2hjk98m4e5x7v3gp1l0o9n2f8c3q2s' -H 'X-Request-ID:request id' \
-X POST http://global-copilot.pre.sg.srv/api/v1/chat \
-d '{"area":1,"site":0,"user_id":"0","org_id":"0","conversation_id":"0","language":2,"chat_history":[{"role":1, "messages":[{"content":"hi~","type":1}]}],"response_mode":1,"request_id":"test_from_curl","version":2}'
