import os
import json

from core.processor import normalize_item_name
from core.schema.item import Item


def load_item_id_name_dict(path, need_normalize=False):
    item_id_name_dict = dict()
    with open(path) as fin:
        raw_data = json.load(fin)
        for item in raw_data[0]["items"]:
            item_name = item["name"]
            if need_normalize:
                item_name = normalize_item_name(item_name)
            item_id = item["id"]
            item_id_name_dict[item_id] = item_name
    return item_id_name_dict


def load_name_item_dict(path):
    name_item_dict = dict()
    with open(path) as fin:
        raw_data = json.load(fin)
        for item in raw_data[0]["items"]:
            item_name = item["name"]
            item_id = item["id"]
            item_cls = Item(
                item_id=item_id,
                item_name=item_name,
                category_id=raw_data[0]['id'],
                category_name=raw_data[0]['name']
            )
            name_item_dict[item_name] = item_cls
    return name_item_dict


def load_item_param_config_dict(path):
    with open(path) as fin:
        return json.load(fin)


def load_item_intro_dict(path):
    with open(path) as fin:
        return json.load(fin)


def load_item_name_list(path):
    item_name_list = list()
    normalized_item_name_list = list()
    with open(path) as fin:
        raw_data = json.load(fin)
        for item in raw_data[0]["items"]:
            item_name_list.append(item["name"])
            normalized_item_name_list.append(normalize_item_name(item["name"]))
    return item_name_list, normalized_item_name_list


def load_item_dataset_id_dict(path):
    item_dataset_id_dict = dict()
    with open(path) as fin:
        raw_dict = json.load(fin)
        for name in raw_dict:
            item_dataset_id_dict[normalize_item_name(name)] = raw_dict[name]
    return item_dataset_id_dict


def load_competitor_map(file_path):
    if os.path.exists(file_path):
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {}