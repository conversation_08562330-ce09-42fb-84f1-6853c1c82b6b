from fastapi import HTTPException

from core.enum.message_type import MessageType
from core.schema.chat_request import ChatRequest
from core.enum.role import Role
from core.schema.translation_base import TranslationRequest
from util.common_util import is_empty


# ToDo(hm): 后面 validator 也可以根据不同的 version 进行拆分
class ChatRequestValidator:

    def validate_chat_request(self, chat_request: ChatRequest):
        # ToDo(hm): 传入的 item_name 要校验吗：比如传入的 item 不在我们的数据集中
        if chat_request.version is None or chat_request.version == 0:
            self.validate_chat_request_430(chat_request)
            return

        if chat_request.version in (1, 2):
            self.validate_chat_request_530(chat_request)
            return

        self.raise_exception(f"暂不支持的 chat version {chat_request.version}", chat_request)

    @staticmethod
    def raise_exception(message: str, chat_request: ChatRequest):
        exception_message = f"收到非法的输入，{message}，chat_request={chat_request}"
        chat_request.logger.warning(exception_message)
        raise HTTPException(status_code=400, detail=exception_message)

    def validate_chat_request_430(self, chat_request: ChatRequest):
        self.common_validate(chat_request)

        if is_empty(chat_request.item_name):
            self.raise_exception("item_name 不能为空", chat_request)

        if is_empty(chat_request.item_id):
            self.raise_exception("item_id 不能为空", chat_request)

        for chat_round in chat_request.chat_history:
            if chat_round is None:
                self.raise_exception("chat_round 不能为空", chat_request)

            if is_empty(chat_round.messages):
                self.raise_exception("chat_round.messages 不能为空", chat_request)

            for message in chat_round.messages:
                if message.type == MessageType.TEXT:
                    if is_empty(message.content):
                        self.raise_exception("message.content 不能为空", chat_request)

                    if len(message.content) >= 2048:
                        self.raise_exception("len(message.content) >= 2048", chat_request)

    def common_validate(self, chat_request: ChatRequest):
        if chat_request.version is not None and chat_request.version not in (0, 1, 2):
            self.raise_exception("version 只能为空, 0, 1, 2", chat_request)

        if chat_request.area is None:
            self.raise_exception("area 不能为空", chat_request)

        if chat_request.language is None:
            self.raise_exception("language 不能为空", chat_request)

        if chat_request.conversation_id is None:
            self.raise_exception("conversation_id 不能为空", chat_request)

        if chat_request.request_id is None:
            self.raise_exception("request_id 不能为空", chat_request)

        if is_empty(chat_request.chat_history):
            self.raise_exception("chat_history 不能为空", chat_request)

        # validate whether the last round is from user
        ending_chat_round = chat_request.chat_history[-1]
        if ending_chat_round.role != Role.USER:
            self.raise_exception("chat_history 最后一条消息必须是用户消息", chat_request)

    # 530 版本：如果用户确认了机型，后端会把问题拿到最后一条消息中
    def validate_chat_request_530(self, chat_request: ChatRequest):
        self.common_validate(chat_request)
        for chat_round in chat_request.chat_history:
            for message in chat_round.messages:
                if message.type in (MessageType.ITEM_CONFIRM, MessageType.ITEM_CANDIDATE):
                    if is_empty(message.item_list):
                        self.raise_exception(f"message.type={message.type.description} 时 item_list 不能为空",
                                             chat_request)
                    continue


# ToDo(hm): move to another file and class
def validate_translation_request(translation_request: TranslationRequest, logger):
    if is_empty(translation_request.content):
        logger.error(f"收到非法的输入，待翻译文本不能为空：{translation_request}")
        raise HTTPException(status_code=400, detail="待翻译文本不能为空")

    if is_empty(translation_request.request_id):
        logger.error(f"收到非法的输入，请求id不能为空：{translation_request}")
        raise HTTPException(status_code=400, detail="请求id不能为空")
