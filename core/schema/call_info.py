from pydantic import BaseModel

from core.enum.call_status import CallStatus


class CallInfo(BaseModel):
    token_count: int = 0
    # 任务提交时间
    task_submit_time: int = 0
    # 调用 llm 时间
    call_llm_time: int = 0
    # llm 调用返回结果的时间
    llm_response_time: int = 0
    # 结果申请使用的时间
    await_start_time: int = 0
    # 结果返回使用的时间
    await_end_time: int = 0
    status: CallStatus = CallStatus.SUBMITTED
    system_prompt: str = ""
    user_prompt: str = ""
    answer: str = ""
    # 用来决定用哪个 llm
    key: int = 0

    def to_dict(self):
        return {
            "消耗 token": self.token_count,
            "排队耗时": max(0, self.call_llm_time - self.task_submit_time),
            "大模型耗时": max(0, self.llm_response_time - self.call_llm_time),
            "结果申请时间": self.await_start_time,
            "结果返回时间": self.await_end_time
        }
