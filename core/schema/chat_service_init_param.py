from dataclasses import dataclass
from typing import Dict, Any, List

from prometheus_client import Counter, Histogram

from core.schema.item import Item


@dataclass
class ChatServiceInitParam:
    """ChatService初始化参数封装类"""
    cur_config: Dict[str, Any]
    item_name_list: List[str]
    normalized_item_name_list: List[str]
    item_name_xiaomi_list: List[str]
    item_name_dataset_id_dict: Dict[str, str]
    item_name_intro_dict: Dict[str, Any]
    item_param_config_dict: Dict[str, Any]
    name_item_dict: Dict[str, Item]
    endpoint_call_counter: Counter
    timer_hist: Histogram

    class Config:
        arbitrary_types_allowed = True

    @classmethod
    def from_dict(cls, **kwargs) -> 'ChatServiceInitParam':
        """从字典创建ChatServiceInitParam实例"""
        return cls(**kwargs)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'cur_config': self.cur_config,
            'item_name_list': self.item_name_list,
            'normalized_item_name_list': self.normalized_item_name_list,
            'item_name_xiaomi_list': self.item_name_xiaomi_list,
            'item_name_dataset_id_dict': self.item_name_dataset_id_dict,
            'item_name_intro_dict': self.item_name_intro_dict,
            'item_param_config_dict': self.item_param_config_dict,
            'name_item_dict': {k: v.to_dict() for k, v in self.name_item_dict.items()},
            # Note: Counter and Histogram objects cannot be serialized to dict
        }
