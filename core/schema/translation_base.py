from pydantic import BaseModel
from typing import Optional


class TranslationRequest(BaseModel):
    request_id: str
    content: str

    def to_dict(self):
        return {
            'request_id': self.request_id,
            'content': self.content
        }

class TranslationResponse(BaseModel):
    request_id: str
    content: Optional[str] = None

    def to_dict(self):
        return {
            'request_id': self.request_id,
            'content': self.content
        }