from dataclasses import dataclass, asdict

@dataclass
class DBRecord:
    conversation_id: str
    question_id: str
    add_time: str
    question_content: str
    response: str
    first_token_elapse: int
    request_receive_time: int
    answer_start_time: int
    answer_finish_time: int
    total_tokens: int
    answer_type: int
    intents: str
    selected_item_names: str
    actual_item_names: str
    item_attributes: str
    model_version: str
    dt: str
    chat_request: str
    prompt: str
    system_language: int
    input_language: int
    output_language: int
    area: int
    version: int
    response_id: str

    def to_dict(self):
        return asdict(self)
    