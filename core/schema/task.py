import asyncio

from core.enum.call_status import CallStatus
from core.schema.call_info import CallInfo
from util.common_util import get_cur_millis


class Task(asyncio.Task):

    def __init__(self, coro, *, loop=None, name=None, key=0):
        self.call_info = CallInfo(task_submit_time=get_cur_millis(), key=key)
        super().__init__(coro, loop=loop, name=name)

    def __await__(self):
        # 拿到底层awaiter对象
        it = super().__await__()
        try:
            self.call_info.await_start_time = get_cur_millis()
            result = yield from it
            # 只有返回结果的时候才设置
            self.call_info.status = CallStatus.USED
            self.call_info.await_end_time = get_cur_millis()
            return result
        except Exception:
            self.call_info.status = CallStatus.FAILED
            raise

    def cancel(self, msg=None):
        self.call_info.status = CallStatus.CANCELLED
        super().cancel(msg)
