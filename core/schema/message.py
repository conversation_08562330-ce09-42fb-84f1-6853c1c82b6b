from typing import Optional, List

from pydantic import BaseModel

from config.chat_config import MESSAGE_PROMPT_PREFIX
from core.enum.language import Language
from core.enum.message_type import MessageType
from core.schema.item import Item


class Message(BaseModel):
    type: Optional[MessageType] = MessageType.TEXT
    content: Optional[str]
    selected_item: Optional[Item] = None
    item_list: Optional[List[Item]] = None

    def to_prompt_str(self, language: Language):
        if self.type in MESSAGE_PROMPT_PREFIX:
            item_list_str = ','.join([item.item_name for item in self.item_list])
            prefix = MESSAGE_PROMPT_PREFIX[self.type][language]
            return f"{prefix}: {item_list_str}"

        return self.content
