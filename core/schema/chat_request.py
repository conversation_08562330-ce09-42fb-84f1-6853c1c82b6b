from typing import List, Optional, Any
from pydantic import BaseModel

from core.enum.area import Area
from core.enum.language import Language
from core.enum.message_type import MessageType
from core.enum.response_mode import ResponseMode
from core.enum.role import Role
from core.schema.message import Message
from core.schema.item import Item
from core.schema.chat_round import ChatRound
from util.string_util import to_json_str


class ChatRequest(BaseModel):
    area: Area
    site: Optional[int] = None
    org_id: Optional[str] = None
    language: Language
    user_id: Optional[str] = None
    conversation_id: str
    response_mode: Optional[ResponseMode] = ResponseMode.STREAMING
    chat_history: List[ChatRound]
    request_id: str
    use_cache_answer: Optional[bool] = False
    debug: Optional[bool] = False
    # 0-430 1-530 2-多轮对话
    version: Optional[int] = 0

    # 430 版本会强制用户选择 item，之后的版本用户可以选择不选择 item，一开始从最后一条 message 中获取，如果中间识别出来则放在这里
    item_id: Optional[str] = None
    item_name: Optional[str] = None
    category_id: Optional[str] = None
    category_name: Optional[str] = None

    # ToDo(hm): put there to ChatRequestVo?
    # for retrieval
    item_name_normalized: Optional[str] = None
    ending_message: Optional[Message] = None
    # 当前吸顶机型
    ending_selected_item: Optional[Item] = None
    rewritten_query: Optional[str] = None
    str_message_list: Optional[List[str]] = None
    joined_str_message: Optional[str] = None
    request_receive_time: Optional[int] = 0
    # 调用 llm 时间
    call_answer_llm_time: Optional[int] = 0
    # 识别出来的语言
    recognize_language: Optional[Language] = None
    logger: Optional[Any] = None
    task_dict: Optional[dict] = None
    http_request: Optional[Any] = None
    answer_intent: Optional[str] = ""
    second_tags: Optional[List[str]] = list()
    actual_item_name_list: Optional[List[str]] = list()
    chat_request_key: Optional[str] = None

    # 调用大模型的输入输出
    prompt_to_answer_dict: Optional[dict] = None

    # 调用大模型的输入输出
    prompt_to_answer_dict: Optional[dict] = None

    def __str__(self):
        return to_json_str(self)

    def __repr__(self):
        return to_json_str(self)

    def to_dict(self):
        """Convert the model to a dictionary for API requests"""
        return self.model_dump(exclude_none=True,
                               exclude={"logger", "call_consumption_dict", "task_dict", "http_request"})

    @classmethod
    def from_dict(cls, data_dict: dict):
        """Convert a dictionary to a ChatRequest instance

        This method handles the conversion of nested structures like chat_history,
        which contains ChatRound objects with Message objects.
        """
        # Handle nested structures
        if 'chat_history' in data_dict:
            chat_history_data = data_dict['chat_history']
            chat_rounds = []

            for round_data in chat_history_data:
                # Process messages in each chat round
                if 'messages' in round_data:
                    messages_data = round_data['messages']
                    messages = []

                    for msg_data in messages_data:
                        # Handle Item objects within messages
                        selected_item = None
                        if 'selected_item' in msg_data and msg_data['selected_item']:
                            selected_item = Item(**msg_data['selected_item'])

                        item_list = None
                        if 'item_list' in msg_data and msg_data['item_list']:
                            item_list = [Item(**item) for item in msg_data['item_list']]

                        # Create Message object with properly constructed nested objects
                        message_kwargs = {k: v for k, v in msg_data.items()
                                          if k not in ['selected_item', 'item_list']}
                        if selected_item:
                            message_kwargs['selected_item'] = selected_item
                        if item_list:
                            message_kwargs['item_list'] = item_list

                        messages.append(Message(**message_kwargs))

                    # Update the round data with processed messages
                    round_data_copy = round_data.copy()
                    round_data_copy['messages'] = messages
                    chat_rounds.append(ChatRound(**round_data_copy))
                else:
                    # If no messages, just create the ChatRound directly
                    chat_rounds.append(ChatRound(**round_data))

            # Update the data_dict with processed chat_history
            data_dict_copy = data_dict.copy()
            data_dict_copy['chat_history'] = chat_rounds

            # Create and return the ChatRequest instance
            return cls(**data_dict_copy)
        else:
            # If no chat_history, just create the ChatRequest directly
            return cls(**data_dict)


def get_simple_chat_request_v1(query, item_name=None, request_id="unknown"):
    if item_name is None:
        message = Message(
            content=query,
            type=MessageType.TEXT
        )
    else:
        message = Message(
            content=query,
            type=MessageType.TEXT,
            selected_item=Item(item_id="123", item_name=item_name, category_id="smartphone",
                               category_name="Smartphone")
        )
    chat_request = ChatRequest(
        area=Area.INDONESIA,
        site=0,
        category_name="0",
        category_id="0",
        user_id="0",
        item_id="0",
        org_id="0",
        conversation_id="0",
        language=Language.INDONESIAN,
        chat_history=[
            ChatRound(
                role=Role.USER,
                messages=[message]
            )
        ],
        response_mode=ResponseMode.STREAMING,
        request_id=request_id,
        debug=True,
        version=1
    )
    return chat_request
