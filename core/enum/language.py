from enum import IntEnum


class Language(IntEnum):
    CHINESE = 1
    ENGLISH = 2
    INDONESIAN = 3

    @staticmethod
    def mapping():
        return {
            "中文": Language.CHINESE,
            "英语": Language.ENGLISH,
            "印尼语": Language.INDONESIAN
            # 根据需要添加更多的严格匹配变体
        }

    @staticmethod
    def reverse_mapping():
        """
        返回语言的反向映射，包含语言枚举到中文名称的映射。
        """
        return {value: key for key, value in Language.mapping().items()}

    @staticmethod
    def from_string(lang_str: str, default_language=ENGLISH):
        return Language.mapping().get(lang_str, default_language)

    @staticmethod
    def get_supported_languages():
        """
        获取支持的语言键。

        :return: 包含所有支持语言键的列表
        """
        return list(Language.mapping().keys())

    def get_chinese_name(self):
        """
        获取语言的中文名称。
        """
        return self.reverse_mapping().get(self)
