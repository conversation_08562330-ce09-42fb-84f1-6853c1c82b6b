# Bad Case Test 页面缓存功能

## 功能概述

为 bad_case_test 页面增加了缓存功能，可以自动保存和加载用户的输入配置，提升用户体验，减少重复输入。

## 功能特性

### 1. 自动缓存加载
- 页面启动时自动加载最近保存的配置
- 包括：机型选择、环境选择、问题输入、并发请求数量
- 如果没有缓存，使用默认配置

### 2. 实时自动保存
- 用户修改任何配置时自动保存（机型、环境、并发数、问题输入）
- 用户点击"提交并发请求"时也会自动保存当前配置
- 完全自动化，无需任何手动操作
- 确保用户的最新配置始终被保存

## 缓存配置内容

缓存的配置包括以下字段：

```python
class BadCaseTestConfig(BaseModel):
    selected_item: str = "UNK"          # 选择的机型
    env: str = "预发"                    # 选择的环境
    user_question: str = ""             # 用户输入的问题
    concurrent_requests: int = 5        # 并发请求数量
    last_updated: Optional[str] = None  # 最后更新时间
```

## 缓存文件位置

缓存文件保存在项目目录下：
```
{project_dir}/tmp/cache/bad_case_test_config.json
```

## 使用方式

### 首次使用
1. 进入 bad_case_test 页面
2. 配置机型、环境、并发数量
3. 输入问题
4. 所有配置会自动保存

### 后续使用
1. 再次进入页面时，自动加载上次的配置
2. 可以直接使用或修改配置
3. 任何修改都会自动保存

## 技术实现

### 核心组件

1. **BadCaseTestConfig**: 配置数据模型
2. **CacheManager**: 缓存管理器，负责读写缓存文件
3. **get_default_bad_case_config()**: 获取默认配置

### 关键功能

1. **配置加载**：页面启动时从缓存文件加载配置
2. **默认值设置**：使用缓存配置设置表单控件的默认值
3. **实时自动保存**：用户修改任何配置时立即自动保存
4. **智能检测**：自动检测配置变化并保存，无需用户干预

### 文件结构

```
util/cache_util.py          # 缓存工具模块
frontend/bad_case_test.py   # 页面实现
tmp/cache/                  # 缓存文件目录
```

## 优势

1. **提升用户体验**：减少重复输入，快速恢复上次配置
2. **完全自动化**：无需任何手动操作，配置变化即时保存
3. **零学习成本**：用户无需了解缓存机制，自然使用即可
4. **可扩展性**：缓存框架可以轻松扩展到其他页面

## 注意事项

1. 缓存文件存储在本地，不同用户/机器的缓存是独立的
2. 缓存文件包含用户输入信息（如问题内容），请注意安全性
3. 如果配置文件损坏，系统会自动使用默认配置
4. 所有配置变化都会自动保存，无需担心数据丢失

## 扩展说明

该缓存功能基于通用的缓存框架实现，可以轻松扩展到其他页面：

1. 定义页面配置类（继承 BaseModel）
2. 使用 CacheManager 进行缓存管理
3. 在页面中集成加载和保存逻辑

这为整个应用的用户体验提升提供了统一的解决方案。
