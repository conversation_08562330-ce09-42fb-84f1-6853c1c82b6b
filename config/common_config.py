import traceback
from fastapi import HTT<PERSON><PERSON>xception
from loguru import logger
from prometheus_client import Histogram, Counter

from config.run_config import RUN_CONFIG_DICT, DATA_BASE_HOST, DATA_BASE_USER, DATA_BASE_PASSWORD, DATA_BASE_NAME, \
    DATA_BASE_PORT, REDIS_HOST, REDIS_PORT, REDIS_PASSWORD, OPENAI_API_KEY, MODEL_SOURCE, MIFY, OPENAI
from core.schema.chat_service_init_param import ChatServiceInitParam
from core.schema.constant import TEST_ENV, ENV_NAME_KEY
from data_loader import load_item_name_list, load_item_param_config_dict, load_item_dataset_id_dict, \
    load_item_intro_dict, load_name_item_dict

from config.model_config import MODEL_CONFIG, ModelConfigKey
from service.chat_service_v0 import ChatServiceV0
from service.chat_service_v1 import ChatServiceV1
from service.model_manager_mify import ModelManagerMify
from service.model_manager_openai import ModelManagerOpenAi
from service.prompt_build_service import PromptBuildService
from service.query_parse_service import QueryParseService
from util.common_util import get_env_by_key
from util.mysql_db_manager import DBManager
from util.redis_manager import RedisManager

# 全局标志
_metrics_initialized = False

# 全局 Prometheus 指标（模块级别定义）
endpoint_call_counter = None
timer_hist = None

def initialize_metrics():
    global endpoint_call_counter, timer_hist, _metrics_initialized
    if not _metrics_initialized:
        endpoint_call_counter = Counter(
            "global_copilot_counter",
            "Total calls to endpoint",
            ["object", "condition"],
        )
        timer_hist = Histogram(
            "global_copilot_timer",
            "Latency of endpoint calls",
            ["object", "condition"],
            buckets=[0, 50, 100, 150, 200, 400, 800, 1000, 2000, 3000, 4000, 5000, 6000, 7000, 8000, 9000, 10000, 20000, 30000],
        )
        _metrics_initialized = True

def get_chat_service_init_param(env_name) -> ChatServiceInitParam:
    try:
        cur_config = MODEL_CONFIG[env_name]
        item_name_list, normalized_item_name_list = load_item_name_list(cur_config[ModelConfigKey.ITEM_ID_NAME_PATH])
        item_name_xiaomi_list, normalized_item_name_xiaomi_list = load_item_name_list(
            cur_config[ModelConfigKey.ITEM_ID_NAME_XIAOMI_PATH])
        item_name_dataset_id_dict = load_item_dataset_id_dict(cur_config[ModelConfigKey.ITEM_NAME_DATASET_ID_PATH])
        item_name_intro_dict = load_item_intro_dict(cur_config[ModelConfigKey.MINET_INTRO_PATH])
        item_param_config_dict = load_item_param_config_dict(cur_config[ModelConfigKey.MINET_PARAM_PATH])
        name_item_dict = load_name_item_dict(cur_config[ModelConfigKey.ITEM_ID_NAME_PATH])
        for item in name_item_dict.values():
            item.is_xiaomi = item.item_name in item_name_xiaomi_list

        initialize_metrics()

        # ToDo(hm): 在检查下这里 endpoint_call_counter
        return ChatServiceInitParam(
            cur_config, item_name_list, normalized_item_name_list, item_name_xiaomi_list, item_name_dataset_id_dict,
            item_name_intro_dict, item_param_config_dict, name_item_dict, endpoint_call_counter,
            timer_hist
        )
    except Exception as e:
        logger.error(f"获取聊天服务初始化参数失败: {traceback.format_exc()}")
        raise


def get_redis_manager():
    env_name = get_env_by_key(ENV_NAME_KEY, TEST_ENV)
    redis_manager = RedisManager(
        host=RUN_CONFIG_DICT[env_name][REDIS_HOST],
        port=int(RUN_CONFIG_DICT[env_name][REDIS_PORT]),
        password=RUN_CONFIG_DICT[env_name][REDIS_PASSWORD]
    )

    return redis_manager


def get_model_manager_by_init_param(init_param, redis_manager=None):
    env_name = get_env_by_key(ENV_NAME_KEY, TEST_ENV)
    model_source = RUN_CONFIG_DICT[env_name][MODEL_SOURCE].strip().lower()
    logger.info(f"model_source: {model_source}")

    if model_source == MIFY:
        model_manager = ModelManagerMify(
            init_param.endpoint_call_counter,
            init_param.timer_hist,
            init_param.cur_config[ModelConfigKey.DATASET_KEY],
            redis_manager
        )
    elif model_source == OPENAI:
        model_manager = ModelManagerOpenAi(
            init_param.endpoint_call_counter,
            init_param.timer_hist,
            init_param.cur_config[ModelConfigKey.DATASET_KEY],
            redis_manager,
            api_key=RUN_CONFIG_DICT[env_name][OPENAI_API_KEY]
        )
    else:
        raise NotImplementedError(f"不支持的模型来源: {model_source}")
    return model_manager


def get_model_manager(redis_manager=None):
    env_name = get_env_by_key(ENV_NAME_KEY, TEST_ENV)
    init_param = get_chat_service_init_param(env_name)
    model_source = RUN_CONFIG_DICT[env_name][MODEL_SOURCE].strip().lower()
    logger.info(f"model_source: {model_source}")

    if model_source == MIFY:
        model_manager = ModelManagerMify(
            init_param.endpoint_call_counter,
            init_param.timer_hist,
            init_param.cur_config[ModelConfigKey.DATASET_KEY],
            redis_manager
        )
    elif model_source == OPENAI:
        model_manager = ModelManagerOpenAi(
            init_param.endpoint_call_counter,
            init_param.timer_hist,
            init_param.cur_config[ModelConfigKey.DATASET_KEY],
            redis_manager,
            api_key=RUN_CONFIG_DICT[env_name][OPENAI_API_KEY]
        )
    else:
        raise NotImplementedError(f"不支持的模型来源: {model_source}")
    return model_manager


def get_chat_service_list(init_param):
    env_name = get_env_by_key(ENV_NAME_KEY, TEST_ENV)
    db = DBManager(
        host=RUN_CONFIG_DICT[env_name][DATA_BASE_HOST],
        user=RUN_CONFIG_DICT[env_name][DATA_BASE_USER],
        password=RUN_CONFIG_DICT[env_name][DATA_BASE_PASSWORD],
        database=RUN_CONFIG_DICT[env_name][DATA_BASE_NAME],
        port=int(RUN_CONFIG_DICT[env_name][DATA_BASE_PORT])
    )

    if not db:
        logger.error("DBManager not initialized")
        raise HTTPException(status_code=500, detail="Database manager not initialized")

    redis_manager = get_redis_manager()

    model_manager = get_model_manager_by_init_param(init_param, redis_manager=redis_manager)
    query_parse_service = QueryParseService(
        init_param.cur_config[ModelConfigKey.API_KEY_JSON],
        init_param.cur_config[ModelConfigKey.API_KEY_JSON_MIFY2],
        init_param.cur_config[ModelConfigKey.API_KEY_JSON_MIFY3],
        model_manager,
        init_param.item_name_list,
        init_param.normalized_item_name_list,
        init_param.item_name_xiaomi_list,
        redis_manager
    )
    prompt_build_service = PromptBuildService(
        init_param.item_param_config_dict,
        model_manager,
        init_param.item_name_dataset_id_dict,
        init_param.item_name_intro_dict,
        db,
        redis_manager
    )
    chat_service_v0 = ChatServiceV0(
        init_param.endpoint_call_counter,
        init_param.timer_hist,
        init_param.cur_config[ModelConfigKey.API_KEY_TEXT],
        query_parse_service,
        prompt_build_service,
        model_manager,
        init_param.normalized_item_name_list,
        init_param.item_name_xiaomi_list,
        db,
        init_param.name_item_dict,
        redis_manager
    )
    chat_service_v1 = ChatServiceV1(
        init_param.endpoint_call_counter,
        init_param.timer_hist,
        init_param.cur_config[ModelConfigKey.API_KEY_TEXT],
        query_parse_service,
        prompt_build_service,
        model_manager,
        init_param.normalized_item_name_list,
        init_param.item_name_xiaomi_list,
        db,
        init_param.name_item_dict,
        redis_manager
    )
    return [chat_service_v0, chat_service_v1]
