[Unit]
Description=global copilot demo service

[Service]
User=root
ExecStart=/usr/bin/env PYTHONUNBUFFERED=1 /home/<USER>/miniforge3/envs/inference12/bin/python /home/<USER>/miniforge3/envs/inference12/bin/streamlit run --server.port 8501 /home/<USER>/workspace/inference/Home.py -- --project_dir /home/<USER>/workspace/inference
Restart=always
RestartSec=5
StandardOutput=append:/home/<USER>/workspace/inference/demo_run.log
StandardError=append:/home/<USER>/workspace/inference/demo_run.log

[Install]
WantedBy=multi-user.target
