import os
from loguru import logger
import sys


def set_default_request_id(record):
    record["extra"].setdefault("request_id", "default_request_id")
    record["extra"].setdefault("chat_request_id", "default_chat_request_id")
    return True  # 始终保留这条日志


def configure_logging():
    """统一日志配置（修复版）"""
    logger.remove()

    # 添加控制台输出（带颜色）
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green>|<level>{level}</level>|<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan>|<yellow>request_id={extra[request_id]}</yellow>|<yellow>chat_request_id={extra[chat_request_id]}</yellow>|<white>{message}</white>",
        level="DEBUG",
        filter=set_default_request_id,
    )

    # 添加文件输出
    workdir = os.environ.get("LOG_DIR", "/home/<USER>")
    logger.add(
        f"{workdir}/log/api.log",
        rotation="10 MB",
        retention="7 days",
        format="{time:YYYY-MM-DD HH:mm:ss.SSS}|{level}|{name}:{function}:{line}|{extra[request_id]}|{extra[chat_request_id]}|{message}",
        level="DEBUG",
        filter=set_default_request_id,
    )
