from pyhive import hive
from TCLIService.ttypes import TOperationState
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import threading
import queue
import time
import traceback
from util.common_util import get_env_by_key
from config.run_config import RUN_CONFIG_DICT, DATA_BASE_HOST
import openpyxl
import os
import datetime

class OnlineDataPuller:
    def __init__(self, user,
                 catalog='iceberg_alsgprc_hadoop',
                 database='sale_copilot_log_save',
                 table_name='global_copilot_question_parse'
                 ):
        """
        初始化Hive连接管理器
        !!! 表创建后无法立即写入数据，给用户授权需要时间, 需要等待一段时间, 具体时间不确定, 可能是1-2分钟

        Args:
            host (str): Hive服务器地址, 数据工厂地址, 默认地址
            user (str): 用户token, 需要用户自己的空间下的token, 数据工厂下的库里面可生成
            catalog (str): 目录集群名称
            database (str): 数据库名称
        """
        self.host = 'proxy-service-thrift-alisgp0-dp.api.xiaomi.net'
        self.user = user
        self.catalog = catalog
        self.database = database
        self.table_name = table_name
        self.config = {"""proxy.engine""": "spark"}
        self.cursor = None
        self.connect()

    def connect(self):
        """建立Hive连接"""
        try:
            self.cursor = hive.connect(
                host=self.host,
                configuration=self.config,
                port=80,
                username=self.user
            ).cursor()
            print("成功连接到Hive服务器")
        except Exception as e:
            print(f"连接失败: {str(e)}")
            raise

    def pull_data(self):
        """
        查询表
        """
        if not os.path.exists("./data/chat_message/"):
            os.mkdir("./data/chat_message/")
        if not os.path.exists("./data/chat_message/online_data.xlsx"):
            select_sql = f"""
            SELECT 
                id,
                conversation_id,
                question_id,
                add_time,
                question_content,
                response,
                first_token_elapse,
                answer_type,
                intents,
                selected_item_names,
                actual_item_names,
                item_attributes,
                model_version,
                dt,
                prompt,
                system_language,
                input_language,
                output_language,
                area
            FROM {self.catalog}.{self.database}.{self.table_name}
            """
            self.cursor.execute(select_sql)
            result = self.cursor.fetchall()
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.append(["id", "conversation_id", "question_id", "add_time", "question_content", "response", "first_token_elapse", "answer_type", "intents", "selected_item_names", "actual_item_names", "item_attributes", "model_version", "dt", "prompt", "system_language", "input_language", "output_language", "area"])
            for row in result:
                ws.append(row)
            wb.save("./data/chat_message/online_data.xlsx")
            wb.close()
        else:
            wb = openpyxl.load_workbook("./data/chat_message/online_data.xlsx")
            add_time_list = []
            for row in wb[wb.sheetnames[0]].iter_rows():
                if row[0].value != "id":
                    add_time_list.append(row[3].value)
            last_add_time = max(add_time_list)
            select_sql = f"""
            SELECT 
                id,
                conversation_id,
                question_id,
                add_time,
                question_content,
                response,
                first_token_elapse,
                answer_type,
                intents,
                selected_item_names,
                actual_item_names,
                item_attributes,
                model_version,
                dt,
                prompt,
                system_language,
                input_language,
                output_language,
                area
            FROM {self.catalog}.{self.database}.{self.table_name}
            WHERE add_time>='{last_add_time}'
            """
            self.cursor.execute(select_sql)
            result = self.cursor.fetchall()
            ws = wb.active
            for row in result:
                ws.append(row)
            wb.save("./data/chat_message/online_data.xlsx")
            wb.close()
    
    def close(self):
        """关闭连接"""
        # self.stop_event.set()
        # self.worker_thread.join()
        if self.cursor:
            try:
                self.cursor.close()
                print("成功关闭连接")
            except Exception as e:
                print(f"关闭连接时出错: {str(e)}")

    def start_pull(self):
        while True:
            time.sleep(10)
            try:
                self.pull_data()
            except Exception as e:
                print(f"数据拉取失败, 发生错误: {str(e)}")
            finally:
                # 关闭连接
                self.close()


# 使用示例
if __name__ == "__main__":
    user = "40932bc1a0454d52af3da1c516e9af89"  # 需要用户自己的空间下的token
    catalog = "iceberg_alsgprc_hadoop"
    database = "sale_copilot_log_save"
    table_name = "global_copilot_question_parse"
    online_data_puller = OnlineDataPuller(user=user)
    online_data_puller.start_pull()
