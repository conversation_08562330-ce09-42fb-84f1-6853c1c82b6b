FROM python:3.12

# 参考「制品库Artifactory用户手册」 https://xiaomi.f.mioffice.cn/wiki/wikk4KPmYUWAeuqXbRw5z98tW4e
# 设置公司内部 pip 源
RUN python -m pip config set global.index-url https://pkgs.d.xiaomi.net/artifactory/api/pypi/pypi-virtual/simple
# 设置公司内部 Debian 源
RUN echo 'deb https://pkgs.d.xiaomi.net/artifactory/debian-remote/debian bookworm main contrib non-free non-free-firmware' > /etc/apt/sources.list && \
    echo 'deb https://pkgs.d.xiaomi.net/artifactory/debian-remote/debian bookworm-updates main contrib non-free non-free-firmware' >> /etc/apt/sources.list && \
    echo 'deb https://pkgs.d.xiaomi.net/artifactory/debian-remote/debian bookworm-backports main contrib non-free non-free-firmware' >> /etc/apt/sources.list && \
    echo 'deb https://pkgs.d.xiaomi.net/artifactory/debian-remote/debian-security bookworm-security main contrib non-free non-free-firmware' >> /etc/apt/sources.list && \
    rm -f /etc/apt/sources.list.d/*.list
# mi-otel-python 需要
# https://stackoverflow.com/questions/11416024/error-installing-python-snappy-snappy-c-h-no-such-file-or-directory/41707800#41707800
RUN apt-get update && apt-get install -y libsnappy-dev

# 安装 OpenTelemetry 依赖（带版本兼容逻辑）
COPY requirements.hera.txt /tmp/requirements.hera.txt
RUN pip install --no-cache-dir -r /tmp/requirements.hera.txt

# 升级pip并安装基础依赖
COPY requirements.txt /tmp/requirements.txt
RUN python -m pip install --upgrade pip --no-cache-dir && \
    pip install --no-cache-dir -r /tmp/requirements.txt uvicorn

# 安装 st 依赖，为了启动 demo
#COPY requirements.st.txt /tmp/requirements.st.txt
#RUN pip install --no-cache-dir -r /tmp/requirements.st.txt

# 设置工作环境
WORKDIR /home/<USER>
COPY . ./bin

EXPOSE 7888
#EXPOSE 8501

ENTRYPOINT ["/home/<USER>/bin/start.sh"]
